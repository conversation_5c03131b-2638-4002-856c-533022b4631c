#!/usr/bin/env python3
"""
空洞填充渲染系统启动脚本
提供简单的命令行界面来选择配置和运行渲染
"""

import sys
import os
import argparse

def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("    体素网格空洞填充渲染系统")
    print("    Voxel Grid Hole Filling Renderer")
    print("=" * 60)
    print()

def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    required_deps = ['torch', 'numpy', 'fvdb']
    optional_deps = ['cv2', 'scipy']
    
    missing_required = []
    missing_optional = []
    
    for dep in required_deps:
        try:
            __import__(dep)
            print(f"  ✓ {dep}")
        except ImportError:
            missing_required.append(dep)
            print(f"  ✗ {dep} (必需)")
    
    for dep in optional_deps:
        try:
            __import__(dep)
            print(f"  ✓ {dep}")
        except ImportError:
            missing_optional.append(dep)
            print(f"  ⚠ {dep} (可选)")
    
    if missing_required:
        print(f"\n错误: 缺少必需依赖: {', '.join(missing_required)}")
        print("请安装: pip install " + " ".join(missing_required))
        return False
    
    if missing_optional:
        print(f"\n警告: 缺少可选依赖: {', '.join(missing_optional)}")
        print("形态学填充功能将不可用")
        print("安装方法: pip install opencv-python scipy")
    
    print("✓ 依赖检查完成\n")
    return True

def show_config_options():
    """显示配置选项"""
    print("可用配置:")
    print("  1. conservative  - 保守填充 (轻微改善，保持原始特征)")
    print("  2. balanced      - 平衡填充 (效果和性能兼顾，推荐)")
    print("  3. aggressive    - 激进填充 (强力消除空洞，可能过度填充)")
    print("  4. no_external_deps - 无外部依赖 (不使用OpenCV)")
    print("  5. custom        - 自定义配置 (手动编辑fill_config.py)")
    print()

def select_config():
    """选择配置"""
    show_config_options()
    
    while True:
        choice = input("请选择配置 (1-5, 默认为2): ").strip()
        
        if not choice:
            choice = "2"
        
        config_map = {
            "1": "conservative",
            "2": "balanced", 
            "3": "aggressive",
            "4": "no_external_deps",
            "5": "custom"
        }
        
        if choice in config_map:
            return config_map[choice]
        else:
            print("无效选择，请输入1-5之间的数字")

def update_config_file(config_name):
    """更新配置文件"""
    if config_name == "custom":
        print("使用自定义配置，请确保已正确编辑 fill_config.py")
        return
    
    try:
        # 读取配置文件
        with open('fill_config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新配置选择
        old_line = 'CURRENT_CONFIG = '
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if line.strip().startswith(old_line):
                lines[i] = f'CURRENT_CONFIG = "{config_name}"  # 选项: "conservative", "aggressive", "balanced", "no_external_deps"'
                break
        
        # 写回文件
        with open('fill_config.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"✓ 配置已更新为: {config_name}")
        
    except Exception as e:
        print(f"警告: 无法更新配置文件: {e}")
        print("请手动编辑 fill_config.py")

def run_renderer():
    """运行渲染器"""
    print("启动渲染器...")
    print("注意: 这可能需要较长时间，请耐心等待")
    print("-" * 40)
    
    try:
        # 导入并运行主程序
        import render_with_fill
        print("渲染完成!")
        
    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
        
    except Exception as e:
        print(f"渲染过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def run_test():
    """运行测试"""
    print("运行填充功能测试...")
    try:
        import test_fill
        print("测试完成!")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

def show_config():
    """显示当前配置"""
    try:
        from fill_config import get_current_config, print_config, validate_config
        config = get_current_config()
        print_config(config)
        validate_config(config)
    except Exception as e:
        print(f"无法显示配置: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='体素网格空洞填充渲染系统')
    parser.add_argument('--config', choices=['conservative', 'balanced', 'aggressive', 'no_external_deps'], 
                       help='直接指定配置类型')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--show-config', action='store_true', help='显示当前配置')
    parser.add_argument('--no-deps-check', action='store_true', help='跳过依赖检查')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 检查依赖
    if not args.no_deps_check:
        if not check_dependencies():
            sys.exit(1)
    
    # 显示配置
    if args.show_config:
        show_config()
        return
    
    # 运行测试
    if args.test:
        run_test()
        return
    
    # 选择配置
    if args.config:
        config_name = args.config
        print(f"使用指定配置: {config_name}")
    else:
        config_name = select_config()
    
    # 更新配置文件
    update_config_file(config_name)
    
    # 确认运行
    print(f"\n准备使用 '{config_name}' 配置运行渲染器")
    
    if config_name == "aggressive":
        print("⚠️  警告: 激进配置可能消耗大量GPU内存和时间")
    elif config_name == "no_external_deps":
        print("ℹ️  信息: 将跳过形态学填充功能")
    
    confirm = input("是否继续? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        run_renderer()
    else:
        print("操作已取消")

if __name__ == "__main__":
    main()
