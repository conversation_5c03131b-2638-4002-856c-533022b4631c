import open3d as o3d
import numpy as np
import os

def load_and_visualize_ply(ply_path, enable_height_coloring=True):
    """
    加载PLY网格文件并使用基于高度的颜色映射进行可视化。
    
    参数:
    ply_path (str): 输入PLY文件的路径
    enable_height_coloring (bool): 是否启用基于高度的颜色映射
    """
    
    # --- 第1步：检查文件存在性 ---
    print(f"1. 正在检查文件 '{ply_path}'...")
    if not os.path.exists(ply_path):
        print(f"错误：文件 '{ply_path}' 不存在！")
        return None
    
    # --- 第2步：加载PLY网格 ---
    print(f"2. 正在加载PLY网格文件...")
    try:
        mesh = o3d.io.read_triangle_mesh(ply_path)
    except Exception as e:
        print(f"加载PLY文件失败: {e}")
        return None
    
    if not mesh.has_vertices():
        print("错误：加载的网格为空！")
        return None
    
    print(f"   加载成功！网格包含 {len(mesh.vertices)} 个顶点, {len(mesh.triangles)} 个三角面")
    
    # --- 第3步：应用颜色映射 ---
    if enable_height_coloring:
        print(f"3. 正在应用基于高度的颜色映射...")
        apply_height_coloring(mesh)
    else:
        print(f"3. 使用默认灰色...")
        mesh.paint_uniform_color([0.7, 0.7, 0.7])
    
    # --- 第4步：计算法线 ---
    print(f"4. 正在计算顶点法线...")
    mesh.compute_vertex_normals()
    
    # --- 第5步：可视化 ---
    print(f"5. 正在显示网格...")
    print("   (可视化窗口) 按 'q' 退出，鼠标拖拽旋转，滚轮缩放")
    
    o3d.visualization.draw_geometries([mesh])
    
    return mesh

def apply_height_coloring(mesh):
    """
    为网格应用基于高度的蓝-绿-红颜色映射。
    
    参数:
    mesh: Open3D TriangleMesh对象
    """
    
    # 1. 获取网格顶点坐标
    vertices = np.asarray(mesh.vertices)
    
    if len(vertices) == 0:
        print("   警告：网格没有顶点，跳过颜色映射")
        return
    
    # 2. 提取高度信息（Z坐标）
    heights = vertices[:, 2]
    
    # 3. 将高度归一化到[0,1]范围
    min_height = np.min(heights)
    max_height = np.max(heights)
    print(f"   高度范围: {min_height:.3f} , {max_height:.3f} (范围: {max_height - min_height:.3f})")
    # if max_height > min_height:
    #     normalized_heights = (heights - min_height) / (max_height - min_height)
    # else:
    #     # 如果所有点高度相同，使用中间颜色（绿色）
    #     normalized_heights = np.full_like(heights, 0.5)
    normalized_heights = (heights - min_height) / (max_height - min_height)
    # 4. 创建蓝-绿-红颜色映射
    colors = np.zeros((len(vertices), 3))
    
    for i, h in enumerate(normalized_heights):
        if h <= 0.35:
            # 纯蓝色区域 (h从0到0.33)
            t = h * 3  # 将[0,0.33]映射到[0,1]
            colors[i] = [0, 0, 1]  # 保持纯蓝色
        elif h <= 0.675:
            # 蓝色到绿色的插值 (h从0.33到0.66)
            t = (h - 0.35) * 3.077  # 将[0.33,0.66]映射到[0,1]
            colors[i] = [0, t, 1-t]  # 从蓝色到绿色
        else:
            # 绿色到红色的插值 (h从0.66到1)
            t = (h - 0.675) * 3.077   # 将[0.66,1]映射到[0,1]
            colors[i] = [t, 1-t, 0]  # 从绿色到红色
    
    # 5. 应用颜色到网格
    mesh.vertex_colors = o3d.utility.Vector3dVector(colors)
    print(f"   已应用基于高度的颜色映射")
    print(f"   高度范围: {min_height:.3f} - {max_height:.3f}")
    print(f"   颜色映射: 蓝色(最低) -> 绿色(中等) -> 红色(最高)")

def get_mesh_info(ply_path):
    """
    获取PLY网格文件的基本信息。
    
    参数:
    ply_path (str): PLY文件路径
    
    返回:
    dict: 包含网格信息的字典
    """
    if not os.path.exists(ply_path):
        return {"error": f"文件 '{ply_path}' 不存在"}
    
    try:
        mesh = o3d.io.read_triangle_mesh(ply_path)
        
        info = {
            "file_path": ply_path,
            "file_size_mb": os.path.getsize(ply_path) / (1024 * 1024),
            "vertices_count": len(mesh.vertices),
            "triangles_count": len(mesh.triangles),
            "has_vertex_colors": mesh.has_vertex_colors(),
            "has_vertex_normals": mesh.has_vertex_normals(),
        }
        
        if len(mesh.vertices) > 0:
            vertices = np.asarray(mesh.vertices)
            info["bounding_box"] = {
                "min": vertices.min(axis=0).tolist(),
                "max": vertices.max(axis=0).tolist(),
                "size": (vertices.max(axis=0) - vertices.min(axis=0)).tolist()
            }
            
            heights = vertices[:, 2]
            info["height_range"] = {
                "min": float(heights.min()),
                "max": float(heights.max()),
                "range": float(heights.max() - heights.min())
            }
        
        return info
        
    except Exception as e:
        return {"error": f"读取文件失败: {e}"}

if __name__ == '__main__':
    # --- 用户配置区域 ---

    INPUT_PLY_FILE = "Mesh.ply"  # 请修改为您的PLY文件路径
    
    ENABLE_HEIGHT_COLORING = True  # 是否启用基于高度的颜色映射
    
    # --- 显示文件信息 ---
    print("=== PLY网格查看器 ===")
    print("正在获取文件信息...")
    
    info = get_mesh_info(INPUT_PLY_FILE)
    if "error" in info:
        print(f"错误: {info['error']}")
    else:
        print(f"\n文件信息:")
        print(f"  文件路径: {info['file_path']}")
        print(f"  文件大小: {info['file_size_mb']:.2f} MB")
        print(f"  顶点数量: {info['vertices_count']:,}")
        print(f"  三角面数量: {info['triangles_count']:,}")
        print(f"  包含顶点颜色: {info['has_vertex_colors']}")
        print(f"  包含顶点法线: {info['has_vertex_normals']}")
        
        if "bounding_box" in info:
            bbox = info["bounding_box"]
            print(f"  边界框大小: [{bbox['size'][0]:.3f}, {bbox['size'][1]:.3f}, {bbox['size'][2]:.3f}]")
            
        if "height_range" in info:
            h_range = info["height_range"]
            print(f"  高度范围: {h_range['min']:.3f} - {h_range['max']:.3f} (范围: {h_range['range']:.3f})")
    
    # --- 加载和可视化 ---
    print(f"\n开始可视化...")
    mesh = load_and_visualize_ply(
        ply_path=INPUT_PLY_FILE,
        enable_height_coloring=ENABLE_HEIGHT_COLORING
    )
    
    if mesh is not None:
        print("\n可视化完成！")
    else:
        print("\n可视化失败！")
