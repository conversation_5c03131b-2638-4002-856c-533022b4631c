# 消融实验运行指南

## 运行步骤

### 方法1: 直接运行完整消融实验
```bash
cd /home/<USER>/code/water_ws/src/SSS_SLAM-uuv/render
python run_ablation.py
```

### 方法2: 手动对比运行
```bash
cd /home/<USER>/code/water_ws/src/SSS_SLAM-uuv/render

# 1. 运行无平滑版本
# 编辑 src/params.py，添加: use_smooth = False
python render.py

# 2. 保存结果图像
# 将生成的图像保存为 baseline_result.png

# 3. 运行有平滑版本  
# 编辑 src/params.py，修改为: use_smooth = True
python render.py

# 4. 保存结果图像
# 将生成的图像保存为 proposed_result.png
```

## 关键评估指标

1. **梯度幅值降低百分比** - 测量噪声减少
2. **总变分降低百分比** - 评估图像平滑度提升
3. **表面粗糙度降低百分比** - 量化地形平滑性改善
4. **细节保持度** - 确保重要特征未丢失

## 预期结果

- 梯度幅值应降低 10-30%
- 总变分应降低 15-40% 
- 表面粗糙度应降低 20-50%
- 细节保持度应 > 90%

## 论文中的表述

在论文中可以这样描述：
"为验证动态平滑模块的有效性，我们进行了消融实验。实验结果显示，相比于无平滑的基线方法，我们的动态平滑模块将梯度幅值降低了X%，总变分降低了Y%，表面粗糙度降低了Z%，同时保持了超过90%的细节保真度。"

## 故障排除

如果遇到错误：
1. 检查所有依赖包是否安装
2. 确认数据路径正确
3. 检查GPU内存是否足够
4. 查看错误日志定位问题