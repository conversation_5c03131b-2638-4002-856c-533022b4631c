#!/usr/bin/env python3
"""
SSS SLAM三种重建方法对比评估工具
对比 render.py(体素渲染) vs SFS_RC.py(Shape from Shading) vs slam_psr.py(泊松表面重建)
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

import numpy as np
import torch
import open3d as o3d
from PIL import Image
import matplotlib.pyplot as plt
import pandas as pd
from reconstruction_metrics import ReconstructionMetrics
import time

class MethodComparison:
    """三种重建方法对比评估类"""
    
    def __init__(self):
        self.metrics = ReconstructionMetrics()
        self.results = {}
        
    def run_all_methods(self, 
                       input_image_path=None,
                       input_pcd_path="merge_map.pcd",
                       output_dir="./comparison_output"):
        """
        运行三种重建方法并保存结果
        
        Args:
            input_image_path: SFS方法需要的输入图像
            input_pcd_path: PSR方法需要的输入点云
            output_dir: 输出目录
        """
        
        os.makedirs(output_dir, exist_ok=True)
        print(f"输出目录: {output_dir}")
        
        # 1. render.py 体素渲染方法
        print("\n" + "="*50)
        print("1. 运行体素渲染方法 (render.py)")
        print("="*50)
        
        voxel_results = self._run_voxel_rendering(output_dir)
        
        # 2. SFS_RC.py Shape from Shading方法
        print("\n" + "="*50) 
        print("2. 运行Shape from Shading方法 (SFS_RC.py)")
        print("="*50)
        
        sfs_results = self._run_sfs_method(input_image_path, output_dir)
        
        # 3. slam_psr.py 泊松表面重建方法
        print("\n" + "="*50)
        print("3. 运行泊松表面重建方法 (slam_psr.py)")  
        print("="*50)
        
        psr_results = self._run_psr_method(input_pcd_path, output_dir)
        
        # 保存路径信息
        self.method_outputs = {
            "voxel": voxel_results,
            "sfs": sfs_results, 
            "psr": psr_results
        }
        
        return self.method_outputs
        
    def _run_voxel_rendering(self, output_dir):
        """运行体素渲染方法"""
        
        # 检查是否有现有的render.py结果
        render_outputs = {
            "height_map": None,
            "binary_map": None,
            "point_cloud": None,
            "mesh": None
        }
        
        # 从scripts目录查找现有结果
        scripts_dir = "../scripts"
        possible_files = [
            ("height_map", "output_1024_height.png"),
            ("binary_map", "output_1024_binary.png"),
            ("height_map", "output_512_height.png"), 
            ("binary_map", "output_512_binary.png")
        ]
        
        for key, filename in possible_files:
            filepath = os.path.join(scripts_dir, filename)
            if os.path.exists(filepath):
                render_outputs[key] = filepath
                print(f"找到体素渲染结果: {filepath}")
        
        # 查找点云和网格结果
        render_dir = "./"
        if os.path.exists(os.path.join(render_dir, "merge_map.pcd")):
            render_outputs["point_cloud"] = os.path.join(render_dir, "merge_map.pcd")
            print(f"找到点云结果: {render_outputs['point_cloud']}")
            
        if os.path.exists(os.path.join(render_dir, "merge_map.ply")):
            render_outputs["mesh"] = os.path.join(render_dir, "merge_map.ply")
            print(f"找到网格结果: {render_outputs['mesh']}")
            
        # 如果没有现有结果，提示需要运行render.py
        if not any(render_outputs.values()):
            print("未找到体素渲染结果，请先运行 render.py")
            
        return render_outputs
        
    def _run_sfs_method(self, input_image_path, output_dir):
        """运行SFS方法"""
        
        if not input_image_path or not os.path.exists(input_image_path):
            print(f"SFS输入图像不存在: {input_image_path}")
            return {"mesh": None}
            
        try:
            # 导入SFS模块
            import SFS_RC
            
            # SFS参数设置
            pixel_width = 0.1  # 根据实际情况调整
            pixel_height = 0.1
            k_factor = 0.5
            
            sfs_output_mesh = os.path.join(output_dir, "sfs_reconstruction.ply")
            
            print(f"运行SFS重建，输入: {input_image_path}")
            start_time = time.time()
            
            # 运行SFS重建
            SFS_RC.sfs_reconstruct_and_visualize_3d(
                image_path=input_image_path,
                pixel_width_meters=pixel_width,
                pixel_height_meters=pixel_height,
                k_factor=k_factor,
                output_mesh_path=sfs_output_mesh,
                show_plots=False
            )
            
            end_time = time.time()
            print(f"SFS重建完成，耗时: {end_time - start_time:.2f}秒")
            
            return {"mesh": sfs_output_mesh}
            
        except Exception as e:
            print(f"SFS重建失败: {e}")
            return {"mesh": None}
            
    def _run_psr_method(self, input_pcd_path, output_dir):
        """运行泊松表面重建方法"""
        
        if not os.path.exists(input_pcd_path):
            print(f"PSR输入点云不存在: {input_pcd_path}")
            return {"mesh": None}
            
        try:
            # 导入PSR模块
            import slam_psr
            
            psr_output_mesh = os.path.join(output_dir, "psr_reconstruction.ply")
            
            print(f"运行PSR重建，输入: {input_pcd_path}")
            start_time = time.time()
            
            # 运行PSR重建
            slam_psr.reconstruct_with_psr(
                pcd_path=input_pcd_path,
                output_path=psr_output_mesh,
                normal_radius=0.3,
                normal_max_nn=10,
                poisson_depth=9,
                density_quantile=0.01
            )
            
            end_time = time.time()
            print(f"PSR重建完成，耗时: {end_time - start_time:.2f}秒")
            
            return {"mesh": psr_output_mesh}
            
        except Exception as e:
            print(f"PSR重建失败: {e}")
            return {"mesh": None}
    
    def compare_methods(self, gt_data=None):
        """
        对比三种方法的重建质量
        
        Args:
            gt_data: Ground truth数据路径字典 {"height": path, "pcd": path, "mesh": path}
        """
        
        if not hasattr(self, 'method_outputs'):
            print("请先运行 run_all_methods()")
            return
            
        print("\n" + "="*60)
        print("开始对比评估三种重建方法")
        print("="*60)
        
        comparison_results = {}
        
        # 1. 网格对网格比较
        meshes = {}
        for method in ["voxel", "sfs", "psr"]:
            mesh_path = self.method_outputs[method].get("mesh")
            if mesh_path and os.path.exists(mesh_path):
                meshes[method] = mesh_path
                
        print(f"\n找到网格文件: {list(meshes.keys())}")
        
        # 计算网格间的RMSE
        mesh_comparisons = {}
        methods = list(meshes.keys())
        
        for i, method1 in enumerate(methods):
            for method2 in methods[i+1:]:
                print(f"\n比较 {method1} vs {method2}:")
                
                try:
                    result = self.metrics.compute_mesh_rmse(
                        meshes[method1], 
                        meshes[method2],
                        num_samples=5000
                    )
                    
                    mesh_comparisons[f"{method1}_vs_{method2}"] = result
                    print(f"  Hausdorff距离: {result['hausdorff']:.4f}")
                    print(f"  平均距离: {result['mean_distance']:.4f}")
                    print(f"  RMSE: {result['rmse']:.4f}")
                    
                except Exception as e:
                    print(f"  比较失败: {e}")
                    mesh_comparisons[f"{method1}_vs_{method2}"] = {"error": str(e)}
        
        # 2. 与Ground Truth比较（如果有）
        gt_comparisons = {}
        if gt_data:
            print(f"\n与Ground Truth比较:")
            
            for method, outputs in self.method_outputs.items():
                method_results = {}
                
                # 网格比较
                if outputs.get("mesh") and gt_data.get("mesh"):
                    try:
                        mesh_gt_result = self.metrics.compute_mesh_rmse(
                            outputs["mesh"], gt_data["mesh"]
                        )
                        method_results["mesh_rmse"] = mesh_gt_result
                        print(f"  {method} 网格RMSE: {mesh_gt_result['rmse']:.4f}")
                    except Exception as e:
                        print(f"  {method} 网格比较失败: {e}")
                
                # 高度图比较（仅体素方法）
                if method == "voxel" and outputs.get("height_map") and gt_data.get("height"):
                    try:
                        height_result = self.metrics.compute_height_map_rmse(
                            outputs["height_map"], gt_data["height"]
                        )
                        method_results["height_rmse"] = height_result
                        print(f"  {method} 高度图RMSE: {height_result['rmse']:.4f}")
                    except Exception as e:
                        print(f"  {method} 高度图比较失败: {e}")
                
                gt_comparisons[method] = method_results
        
        # 3. 方法特征统计
        method_stats = {}
        for method, outputs in self.method_outputs.items():
            stats = {}
            
            # 网格统计
            if outputs.get("mesh") and os.path.exists(outputs["mesh"]):
                try:
                    mesh = o3d.io.read_triangle_mesh(outputs["mesh"])
                    stats["vertices"] = len(mesh.vertices)
                    stats["triangles"] = len(mesh.triangles)
                    
                    if len(mesh.vertices) > 0:
                        vertices = np.asarray(mesh.vertices)
                        stats["bbox_volume"] = np.prod(vertices.max(axis=0) - vertices.min(axis=0))
                        
                except Exception as e:
                    stats["mesh_error"] = str(e)
            
            method_stats[method] = stats
        
        # 保存对比结果
        comparison_results = {
            "method_comparisons": mesh_comparisons,
            "gt_comparisons": gt_comparisons,
            "method_statistics": method_stats
        }
        
        self.comparison_results = comparison_results
        return comparison_results
    
    def generate_report(self, output_path="comparison_report.txt"):
        """生成对比报告"""
        
        if not hasattr(self, 'comparison_results'):
            print("请先运行对比评估")
            return
            
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("SSS SLAM三种重建方法对比报告\n")
            f.write("="*60 + "\n\n")
            
            # 方法统计信息
            f.write("1. 方法统计信息\n")
            f.write("-"*30 + "\n")
            for method, stats in self.comparison_results["method_statistics"].items():
                f.write(f"\n{method.upper()}方法:\n")
                for key, value in stats.items():
                    f.write(f"  {key}: {value}\n")
            
            # 方法间比较
            f.write(f"\n2. 方法间比较结果\n")
            f.write("-"*30 + "\n")
            for comparison, results in self.comparison_results["method_comparisons"].items():
                f.write(f"\n{comparison}:\n")
                if "error" in results:
                    f.write(f"  错误: {results['error']}\n")
                else:
                    for metric, value in results.items():
                        f.write(f"  {metric}: {value:.4f}\n")
            
            # GT比较（如果有）
            if self.comparison_results["gt_comparisons"]:
                f.write(f"\n3. 与Ground Truth比较\n")
                f.write("-"*30 + "\n")
                for method, results in self.comparison_results["gt_comparisons"].items():
                    f.write(f"\n{method.upper()}方法:\n")
                    for metric_type, metrics in results.items():
                        f.write(f"  {metric_type}:\n")
                        for key, value in metrics.items():
                            if isinstance(value, (int, float)):
                                f.write(f"    {key}: {value:.4f}\n")
                            else:
                                f.write(f"    {key}: {value}\n")
        
        print(f"对比报告已保存到: {output_path}")

def main():
    """主函数示例"""
    
    print("SSS SLAM三种重建方法对比工具")
    print("="*50)
    
    # 初始化对比器
    comparator = MethodComparison()
    
    # 设置输入文件（根据实际情况修改）
    input_image_path = "../scripts/crop_L.png"  # SFS输入图像
    input_pcd_path = "merge_map.pcd"             # PSR输入点云
    output_dir = "./method_comparison_output"
    
    # 运行三种方法
    print("\n步骤1: 运行三种重建方法...")
    method_outputs = comparator.run_all_methods(
        input_image_path=input_image_path,
        input_pcd_path=input_pcd_path,
        output_dir=output_dir
    )
    
    # 进行对比评估
    print("\n步骤2: 对比评估...")
    
    # 如果有Ground Truth，设置路径
    gt_data = {
        # "height": "/path/to/gt_height.png",
        # "pcd": "/path/to/gt.pcd", 
        # "mesh": "/path/to/gt.ply"
    }
    
    comparison_results = comparator.compare_methods(gt_data if any(gt_data.values()) else None)
    
    # 生成报告
    print("\n步骤3: 生成对比报告...")
    report_path = os.path.join(output_dir, "comparison_report.txt")
    comparator.generate_report(report_path)
    
    print(f"\n对比评估完成！")
    print(f"结果保存在: {output_dir}")
    print(f"报告文件: {report_path}")

if __name__ == "__main__":
    main()