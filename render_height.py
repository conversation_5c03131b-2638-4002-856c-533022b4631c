import torch
import fvdb
import math
import time

import numpy as np
import src.loadKeypings as lk
import src.scenePC as spc
import src.grid2image as g2i
import src.confidence_gaussian as cg
import src.params_fill as params
import polyscope as ps
import point_cloud_utils as pcu
from fvdb import Jagged<PERSON>ens<PERSON>, GridBatch

from torch.utils.tensorboard import SummaryWriter

def inv_sigmoid(x):
    return -math.log(1 / x - 1)
def fill_holes_via_heightmap(grid: GridBatch, alpha: torch.Tensor, bbox_min, bbox_max):
    """
    通过高度图填补空洞
    """
    print("通过高度图填补空洞...")
    
    # 生成当前高度图
    current_height = g2i.grid_to_height(grid, bbox_min, bbox_max, mod="mean")
    
    # 检测空洞（高度为0或NaN的区域）
    height_data = current_height[0].cpu().numpy()
    hole_mask = (height_data == 0) | np.isnan(height_data)
    
    if np.any(hole_mask):
        print(f"检测到 {np.sum(hole_mask)} 个空洞像素")
        
        # 使用形态学操作填补空洞
        from scipy import ndimage
        from scipy.interpolate import griddata
        
        # 方法1：最近邻插值填补
        filled_height = height_data.copy()
        valid_mask = ~hole_mask
        
        if np.any(valid_mask):
            # 获取有效点的坐标和值
            valid_coords = np.column_stack(np.where(valid_mask))
            valid_values = height_data[valid_mask]
            
            # 获取需要填补的点的坐标
            hole_coords = np.column_stack(np.where(hole_mask))
            
            # 使用最近邻插值
            if len(hole_coords) > 0 and len(valid_coords) > 0:
                interpolated_values = griddata(
                    valid_coords, valid_values, hole_coords, 
                    method='nearest', fill_value=0
                )
                filled_height[hole_mask] = interpolated_values
        
        # 将填补后的高度图转换回体素
        filled_coords = height_to_voxel_coords(
            torch.from_numpy(filled_height).to(grid.device), 
            bbox_min, bbox_max, grid.voxel_sizes[0]
        )
        
        # 启用新的体素
        if len(filled_coords) > 0:
            grid.enable_ijk(JaggedTensor([filled_coords]))
            
            # 为新体素设置适当的透明度
            new_alpha = torch.full(
                (len(filled_coords),), 
                inv_sigmoid(0.3), 
                device=grid.device, 
                requires_grad=True
            )
            alpha.data = torch.cat([alpha.data, new_alpha])
    
    return grid, alpha

def height_to_voxel_coords(height_map, bbox_min, bbox_max, voxel_size):
    """将高度图转换为体素坐标"""
    coords = []
    h, w = height_map.shape
    
    for i in range(h):
        for j in range(w):
            if height_map[i, j] > 0:  # 只处理非零高度
                # 计算世界坐标
                x = bbox_min[0] + j * voxel_size
                y = bbox_min[1] + i * voxel_size
                z = bbox_min[2] + height_map[i, j] * voxel_size
                
                # 转换为体素坐标
                voxel_coord = torch.tensor([
                    int(x / voxel_size),
                    int(y / voxel_size), 
                    int(z / voxel_size)
                ], device=height_map.device)
                
                coords.append(voxel_coord)
    
    return torch.stack(coords) if coords else torch.empty((0, 3), device=height_map.device)


# 其余函数保持不变...
def visualize_grid_color(grid: GridBatch, rgb: JaggedTensor, ignore_disabled: bool = False):
    for b in range(grid.grid_count):
        grid_mask = grid.enabled_mask[b].jdata.cpu().numpy()
        if ignore_disabled:
            grid_mask.fill(True)

        grid_mesh = pcu.voxel_grid_geometry(
            grid.ijk[b].jdata.cpu().numpy()[grid_mask],
            0.1,
            grid.voxel_sizes[b].cpu().numpy())
        grid_color = rgb[b].jdata.cpu().numpy()[grid_mask].repeat(8, axis=0).reshape(-1, 3)

        ps.register_surface_mesh(
            f"grid_{b}", grid_mesh[0], grid_mesh[1], enabled=True
        ).add_color_quantity("color", grid_color, enabled=True)

def visualize_grid(grid: GridBatch):
    ps.init()

    # 设置背景色为黑色
    ps.set_background_color((0.0, 0.0, 0.0))  # RGB黑色

    feature = grid.grid_to_world(grid.ijk.float())
    z_values = feature.jdata[:, 2]

    z_min = z_values.min()
    z_max = z_values.max()
    z_values = (z_values - z_min) / (z_max - z_min)
    
    # 创建颜色数组
    colors = torch.zeros_like(feature.jdata)
    
    # 最低平面设置为白色 (z值小于0.42的部分)
    bottom_mask = z_values < 0.3
    above_mask = z_values >= 0.3
    grid.disable_ijk(grid.ijk.r_masked_select(bottom_mask))
    # 最低平面设为白色
    colors[bottom_mask] = 1.0  # RGB都设为1，呈现白色
    
    # 高于底部的部分处理
    above_indices = torch.where(above_mask)[0]
    above_z_values = z_values[above_indices]
    
    # 重新归一化到[0,1]
    normalized_z = (above_z_values - 0.3) / 0.7
    
    # 蓝色到绿色 (0 <= normalized_z <= 0.5)
    blue_to_green_indices = above_indices[normalized_z <= 0.5]
    blue_to_green_z = normalized_z[normalized_z <= 0.5]
    
    colors[blue_to_green_indices, 0] = 0  # 红色分量为0
    colors[blue_to_green_indices, 1] = blue_to_green_z * 2  # 绿色分量从0增加到1
    colors[blue_to_green_indices, 2] = 1 - blue_to_green_z * 2  # 蓝色分量从1减少到0
    
    # 绿色到红色 (0.5 < normalized_z <= 1.0)
    green_to_red_indices = above_indices[normalized_z > 0.5]
    green_to_red_z = normalized_z[normalized_z > 0.5]
    
    colors[green_to_red_indices, 0] = (green_to_red_z - 0.5) * 2  # 红色分量从0增加到1
    colors[green_to_red_indices, 1] = 1 - (green_to_red_z - 0.5) * 2  # 绿色分量从1减少到0
    colors[green_to_red_indices, 2] = 0  # 蓝色分量为0

    # 将颜色赋值给feature
    feature.jdata = colors

    # Visualization
    ps.remove_all_structures()
    visualize_grid_color(grid, feature)
    ps.set_ground_plane_mode("none")
    ps.show()

def render_opacity(grid: fvdb.GridBatch, feature: torch.Tensor, ray_orig=None, ray_dir=None):
    assert ray_orig is not None and ray_dir is not None
    if torch.isnan(feature).any():
        print("feature contains NaN")
        print("feature:", feature)

    pack_info, voxel_inds, out_times = grid.voxels_along_rays(ray_orig, ray_dir, 128, 0.0)
    pack_info = pack_info.jdata
    out_times = out_times.jdata
    voxel_inds = grid.ijk_to_index(voxel_inds).jdata

    rgb, depth, opacity, _, _ = fvdb.utils.volume_render(
        sigmas=-torch.log(1 - feature[voxel_inds]),
        rgbs=torch.ones((voxel_inds.shape[0], 1), device=grid.device),
        deltaTs=torch.ones(voxel_inds.shape[0], device=grid.device),
        ts=out_times.mean(1),
        packInfo=pack_info, transmittanceThresh=0.0
    )
    return opacity

if __name__ == "__main__":

  # 读取声纳回波数据
  keypings = lk.load_keypings(params.kp_dir, params.parse_ratio)
  if params.is_multi:
    keypings2 = lk.load_keypings(params.kp2_dir, params.parse_ratio)
    keypings.extend(keypings2)
  print("load keypings complated")

  # 初始化点云和场景信息
  init_PC, scene_info = spc.createPC(keypings, params.print_origPC, params.origPC_dir)
  print("init point cloud complated")

  # 进行射线近似
  render_masks = []
  ray_orig, ray_dir = [], []
  ray_opacity = []
  #keyping id ; keyping 
  for kid, kp in enumerate(keypings):
    render_masks.append(kp.mask)
    ro, rd = kp.get_rays()
    ray_orig.append(ro)
    ray_dir.append(rd)
    ray_opacity.append(render_masks[kid].flatten())
  
  ray_orig = np.concatenate(ray_orig, axis=0)
  ray_orig = (ray_orig - scene_info.center) / scene_info.scale # 射线起点归一化
  ray_dir = np.concatenate(ray_dir, axis=0)
  ray_opacity = np.concatenate(ray_opacity, axis=0)

  # 检测每一行是否包含 NaN 值
  rows_without_nan = ~np.isnan(ray_orig).any(axis=1)

  #更严谨的做法######
  # nan_mask = np.isnan(ray_orig).any(axis=1) | np.isnan(ray_dir).any(axis=1) | np.isnan(ray_opacity)
  # rows_without_nan = ~nan_mask
  #更严谨的做法######

  # 使用布尔索引过滤掉包含 NaN 的行
  ray_orig = ray_orig[rows_without_nan]
  ray_dir = ray_dir[rows_without_nan]
  ray_opacity = ray_opacity[rows_without_nan]

  ray_orig = torch.from_numpy(ray_orig).to("cuda").float()
  ray_dir = torch.from_numpy(ray_dir).to("cuda").float()
  ray_opacity = torch.from_numpy(ray_opacity).to("cuda").float()
  
  grid = fvdb.sparse_grid_from_points(
      init_PC,
      voxel_sizes=1.0 / (params.render_res - 1),
      origins=[0.0] * 3,
      mutable=True
  )

  # grid = fvdb.sparse_grid_from_dense(
  #   num_grids=1, dense_dims=[params.render_res] * 3,
  #   voxel_sizes=[1.0 / (params.render_res - 1)] * 3,
  #   origins=[0.0] * 3,
  #   mutable=True
  # )
  grid = grid.to("cuda")
  
  z_board = 5
  bbox_min = (grid.bbox[:, 0] - torch.tensor([5, 5, z_board]).long().to("cuda")).squeeze()
  bbox_max = (grid.bbox[:, 1] + torch.tensor([5, 5, z_board]).long().to("cuda")).squeeze()

  height_image0 = g2i.grid_to_height(grid, bbox_min, bbox_max, mod="mean") # 生成地底图

  # visualize_grid(grid) # 打印初始化网格
  grid = grid.coarsened_grid(4)
  # # visualize_grid(grid) # 打印降采样网格
  grid = grid.subdivided_grid(4)
  # visualize_grid(grid) # 打印升采样网格
  
  #---------------------------------------------------
  
  alpha = torch.full((grid.total_voxels, ), inv_sigmoid(params.init_alpha), device=grid.device, requires_grad=True)

  if torch.isnan(alpha).any():
    print("alpha contains NaN")
    print("alpha:", alpha)

  optimizer = torch.optim.Adam([alpha], lr=1.0)
  merge_images = []
  writer = SummaryWriter(log_dir=params.log_dir)  # 日志保存路径
  # 在训练循环前记录开始时间
  print(f"当前显存占用: {torch.cuda.memory_allocated() / 1024**2:.2f} MB")
  total_start = time.time()
  for it in range(100):
    
    sample_num = params.sample_num
    if params.random_sample:
      # 随机索引
      sub_inds = torch.randint(0, ray_orig.shape[0], (sample_num, ), device=grid.device)
    else:
      # 按顺序索引
      sub_inds = torch.arange(it*sample_num, (it+1)*sample_num) % ray_orig.shape[0]
      sub_inds = sub_inds.to(grid.device)

    pd_opacity = render_opacity(grid, torch.sigmoid(alpha), ray_orig=ray_orig[sub_inds], ray_dir=ray_dir[sub_inds])
    gt_opacity = ray_opacity[sub_inds]
    # 检查 pd_opacity 是否包含 NaN
    pd_opacity = torch.nan_to_num(pd_opacity, nan=0.0)
    loss = torch.mean(torch.abs(pd_opacity - gt_opacity))
    print("Iter", it, "Loss:", loss.item())
    writer.add_scalar('Loss/train', loss.item(), it)
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()

    alpha.data = torch.where(torch.isnan(alpha), torch.zeros_like(alpha), alpha)
    
    if it > 0 and it % 5 == 0:
      torch.cuda.empty_cache()
      with torch.no_grad():
        # disable
        bad_mask = torch.sigmoid(alpha) < 0.1
        grid.disable_ijk(grid.ijk.r_masked_select(bad_mask))

        # random revive
        if it < 20:
            enable_mask = torch.rand(grid.total_voxels, device=grid.device) < 0.01
            grid.enable_ijk(grid.ijk.r_masked_select(enable_mask))

        height_image1 = g2i.grid_to_height(grid, bbox_min, bbox_max)
        # if it % 10 == 0:
        #   g2i.show_images(height_image1)
        #   visualize_grid(grid)
        merge_image = g2i.merge_height(height_image0, height_image1)
        merge_images.append(merge_image)

  writer.close()
  
  visualize_grid(grid)
  final_image = merge_images[-1]
  # 初始化高度图
  g2i.show_images(height_image0)
  # 最终优化的高度图
  g2i.show_images(height_image1)
  # 合并高度图
  g2i.show_images(final_image)

  # 应用高斯卷积 - 动态平滑模块
  use_smooth = params.use_smooth if hasattr(params, 'use_smooth') else True
  
  if use_smooth:
    # 把11, 1.5, 1和11, 1.5, 2合起来不错
    # !11， 0.75，3也不错 okok
    # 3, 1.5. 2
    final_image = cg.confidence_gaussian(final_image, kernel_size = 11, sigma = 0.75, iter = 3)
    # 平滑后的高度图
    # final_image = cg.confidence_gaussian(final_image, kernel_size = 9, sigma = 1, iter = 4)
    print("Applied dynamic smoothing")
    
  g2i.show_images(final_image)

  # 消融实验：计算图像质量指标
  def calculate_quality_metrics(image_tensor):
    """计算图像质量指标用于消融实验"""
    if len(image_tensor.shape) > 2:
      image = image_tensor[0].cpu().numpy()  # 取高度通道
    else:
      image = image_tensor.cpu().numpy()
    
    # 梯度幅值
    grad_x = torch.diff(image_tensor[0], dim=1, prepend=image_tensor[0][:, :1])
    grad_y = torch.diff(image_tensor[0], dim=0, prepend=image_tensor[0][:1, :])
    gradient_magnitude = torch.sqrt(grad_x**2 + grad_y**2)
    avg_gradient = torch.mean(gradient_magnitude).item()
    
    # 总变分
    tv_x = torch.mean(torch.abs(torch.diff(image_tensor[0], dim=1))).item()
    tv_y = torch.mean(torch.abs(torch.diff(image_tensor[0], dim=0))).item()
    total_variation = tv_x + tv_y
    
    # 表面粗糙度（拉普拉斯）
    laplacian = torch.zeros_like(image_tensor[0])
    if image_tensor[0].shape[0] > 2 and image_tensor[0].shape[1] > 2:
      laplacian[1:-1, 1:-1] = (image_tensor[0][:-2, 1:-1] + image_tensor[0][2:, 1:-1] + 
                              image_tensor[0][1:-1, :-2] + image_tensor[0][1:-1, 2:] - 
                              4 * image_tensor[0][1:-1, 1:-1])
    surface_roughness = torch.std(laplacian).item()
    
    print(f"\n=== 图像质量指标 (use_smooth={use_smooth}) ===")
    print(f"平均梯度幅值: {avg_gradient:.6f}")
    print(f"总变分: {total_variation:.6f}")
    print(f"表面粗糙度: {surface_roughness:.6f}")
    print(f"平均高度: {torch.mean(image_tensor[0]).item():.6f}")
    print(f"高度标准差: {torch.std(image_tensor[0]).item():.6f}")
    print("="*50)
    
    return {
      'gradient_magnitude': avg_gradient,
      'total_variation': total_variation,
      'surface_roughness': surface_roughness,
      'use_smooth': use_smooth
    }

  # 计算最终图像的质量指标
  quality_metrics = calculate_quality_metrics(final_image)

  z_scale = (bbox_max-bbox_min)[2] - z_board * 2
  # 打印最终的稠密点云
  pc = spc.image_to_pc(final_image[0], grid.voxel_sizes, z_scale)
  if params.print_finalPC:
    spc.print_pointcloud(pc, params.final_pc_dir)

  grid = fvdb.sparse_grid_from_points(
      pc,
      voxel_sizes=1.0 / (params.render_res - 1),
      origins=[0.0] * 3,
      mutable=True
  )
  print("执行最终空洞填补...")
  grid, alpha = fill_holes_via_heightmap(grid, alpha, bbox_min, bbox_max)
      
  visualize_grid(grid)
