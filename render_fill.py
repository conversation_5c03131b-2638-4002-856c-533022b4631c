import torch
import fvdb
import math
import time

import numpy as np
import src.loadKeypings as lk
import src.scenePC as spc
import src.grid2image as g2i
import src.confidence_gaussian as cg
import src.params_fill as params
import polyscope as ps
import point_cloud_utils as pcu
from fvdb import Jag<PERSON><PERSON>ens<PERSON>, GridBatch

from torch.utils.tensorboard import SummaryWriter

def inv_sigmoid(x):
   return -math.log(1 / x - 1)

def fill_holes_via_heightmap_improved(grid: GridBatch, alpha: torch.Tensor, bbox_min, bbox_max, 
                                   min_hole_size=3, max_fill_ratio=0.3):
   """
   改进的空洞填补函数
   Args:
       min_hole_size: 最小填补空洞大小（像素）
       max_fill_ratio: 最大填补比例（避免过度填补）
   """
   print("通过改进的高度图填补空洞...")
   
   current_height = g2i.grid_to_height(grid, bbox_min, bbox_max, mod="mean")
   height_data = current_height[0].cpu().numpy()
   hole_mask = (height_data == 0) | np.isnan(height_data)
   
   if np.any(hole_mask):
       total_pixels = height_data.size
       hole_pixels = np.sum(hole_mask)
       hole_ratio = hole_pixels / total_pixels
       
       print(f"检测到 {hole_pixels} 个空洞像素 ({hole_ratio:.2%} of total)")
       
       # 如果空洞太多，限制填补数量
       if hole_ratio > max_fill_ratio:
           print(f"空洞比例过高，仅填补最重要的区域...")
       
       from scipy import ndimage
       from scipy.interpolate import griddata
       
       # 使用形态学操作去除小的孤立空洞
       if min_hole_size > 1:
           # 标记连通区域
           labeled_holes, num_holes = ndimage.label(hole_mask)
           
           # 过滤小空洞
           for i in range(1, num_holes + 1):
               hole_size = np.sum(labeled_holes == i)
               if hole_size < min_hole_size:
                   hole_mask[labeled_holes == i] = False
       
       # 执行插值填补
       filled_height = height_data.copy()
       valid_mask = ~hole_mask
       
       if np.any(valid_mask) and np.any(hole_mask):
           valid_coords = np.column_stack(np.where(valid_mask))
           valid_values = height_data[valid_mask]
           hole_coords = np.column_stack(np.where(hole_mask))
           
           if len(hole_coords) > 0 and len(valid_coords) > 0:
               # 尝试使用线性插值，失败则使用最近邻
               try:
                   interpolated_values = griddata(
                       valid_coords, valid_values, hole_coords, 
                       method='linear', fill_value=np.nan
                   )
                   # 对仍然是NaN的点使用最近邻
                   nan_mask = np.isnan(interpolated_values)
                   if np.any(nan_mask):
                       interpolated_values[nan_mask] = griddata(
                           valid_coords, valid_values, hole_coords[nan_mask], 
                           method='nearest', fill_value=0
                       )
               except:
                   interpolated_values = griddata(
                       valid_coords, valid_values, hole_coords, 
                       method='nearest', fill_value=0
                   )
               
               filled_height[hole_mask] = interpolated_values
       
       # 转换回体素并更新grid
       filled_coords = height_to_voxel_coords(
           torch.from_numpy(filled_height).to(grid.device), 
           bbox_min, bbox_max, grid.voxel_sizes[0]
       )
       
       if len(filled_coords) > 0:
           # 过滤掉已存在的体素
           existing_coords_set = set()
           if grid.total_voxels > 0:
               existing_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
               existing_coords_set = set(map(tuple, existing_coords.cpu().numpy()))
           
           new_coords = []
           for coord in filled_coords:
               coord_tuple = tuple(coord.cpu().numpy())
               if coord_tuple not in existing_coords_set:
                   new_coords.append(coord)
           
           if new_coords:
               new_coords = torch.stack(new_coords)
               print(f"添加 {len(new_coords)} 个新体素来填补空洞")
               
               grid.enable_ijk(JaggedTensor([new_coords]))
               
               # 为新体素设置适当的透明度
               new_alpha = torch.full(
                   (len(new_coords),), 
                   inv_sigmoid(0.2),  # 稍微降低透明度
                   device=grid.device, 
                   requires_grad=True
               )
               alpha.data = torch.cat([alpha.data, new_alpha])
   
   return grid, alpha

def height_to_voxel_coords(height_map, bbox_min, bbox_max, voxel_size):
   """将高度图转换为体素坐标"""
   coords = []
   h, w = height_map.shape
   
   for i in range(h):
       for j in range(w):
           if height_map[i, j] > 0:  # 只处理非零高度
               # 计算世界坐标
               x = bbox_min[0] + j * voxel_size
               y = bbox_min[1] + i * voxel_size
               z = bbox_min[2] + height_map[i, j] * voxel_size
               
               # 转换为体素坐标
               voxel_coord = torch.tensor([
                   int(x / voxel_size),
                   int(y / voxel_size), 
                   int(z / voxel_size)
               ], device=height_map.device)
               
               coords.append(voxel_coord)
   
   return torch.stack(coords) if coords else torch.empty((0, 3), device=height_map.device)

def visualize_grid_color(grid: GridBatch, rgb: JaggedTensor, ignore_disabled: bool = False):
   for b in range(grid.grid_count):
       grid_mask = grid.enabled_mask[b].jdata.cpu().numpy()
       if ignore_disabled:
           grid_mask.fill(True)

       grid_mesh = pcu.voxel_grid_geometry(
           grid.ijk[b].jdata.cpu().numpy()[grid_mask],
           0.1,
           grid.voxel_sizes[b].cpu().numpy())
       grid_color = rgb[b].jdata.cpu().numpy()[grid_mask].repeat(8, axis=0).reshape(-1, 3)

       ps.register_surface_mesh(
           f"grid_{b}", grid_mesh[0], grid_mesh[1], enabled=True
       ).add_color_quantity("color", grid_color, enabled=True)

def visualize_grid(grid: GridBatch):
   ps.init()
   
   feature = grid.grid_to_world(grid.ijk.float())
   z_values = feature.jdata[:, 2]

   z_min = z_values.min()
   z_max = z_values.max()
   z_values = (z_values - z_min) / (z_max - z_min)
   
   # 创建颜色数组
   colors = torch.zeros_like(feature.jdata)
   
   # 最低平面设置为白色 (z值小于0.42的部分)
   bottom_mask = z_values < 0.3
   above_mask = z_values >= 0.3
   grid.disable_ijk(grid.ijk.r_masked_select(bottom_mask))
   # 最低平面设为白色
   colors[bottom_mask] = 1.0  # RGB都设为1，呈现白色
   
   # 高于底部的部分处理
   above_indices = torch.where(above_mask)[0]
   above_z_values = z_values[above_indices]
   
   # 重新归一化到[0,1]
   normalized_z = (above_z_values - 0.3) / 0.7
   
   # 蓝色到绿色 (0 <= normalized_z <= 0.5)
   blue_to_green_indices = above_indices[normalized_z <= 0.5]
   blue_to_green_z = normalized_z[normalized_z <= 0.5]
   
   colors[blue_to_green_indices, 0] = 0  # 红色分量为0
   colors[blue_to_green_indices, 1] = blue_to_green_z * 2  # 绿色分量从0增加到1
   colors[blue_to_green_indices, 2] = 1 - blue_to_green_z * 2  # 蓝色分量从1减少到0
   
   # 绿色到红色 (0.5 < normalized_z <= 1.0)
   green_to_red_indices = above_indices[normalized_z > 0.5]
   green_to_red_z = normalized_z[normalized_z > 0.5]
   
   colors[green_to_red_indices, 0] = (green_to_red_z - 0.5) * 2  # 红色分量从0增加到1
   colors[green_to_red_indices, 1] = 1 - (green_to_red_z - 0.5) * 2  # 绿色分量从1减少到0
   colors[green_to_red_indices, 2] = 0  # 蓝色分量为0

   # 将颜色赋值给feature
   feature.jdata = colors

   # Visualization
   ps.remove_all_structures()
   visualize_grid_color(grid, feature)
   ps.set_ground_plane_mode("none")
   ps.show()

def render_opacity(grid: fvdb.GridBatch, feature: torch.Tensor, ray_orig=None, ray_dir=None):
   assert ray_orig is not None and ray_dir is not None
   if torch.isnan(feature).any():
       print("feature contains NaN")
       print("feature:", feature)

   pack_info, voxel_inds, out_times = grid.voxels_along_rays(ray_orig, ray_dir, 128, 0.0)
   pack_info = pack_info.jdata
   out_times = out_times.jdata
   voxel_inds = grid.ijk_to_index(voxel_inds).jdata

   rgb, depth, opacity, _, _ = fvdb.utils.volume_render(
       sigmas=-torch.log(1 - feature[voxel_inds]),
       rgbs=torch.ones((voxel_inds.shape[0], 1), device=grid.device),
       deltaTs=torch.ones(voxel_inds.shape[0], device=grid.device),
       ts=out_times.mean(1),
       packInfo=pack_info, transmittanceThresh=0.0
   )
   return opacity

if __name__ == "__main__":

 # 读取声纳回波数据
 keypings = lk.load_keypings(params.kp_dir, params.parse_ratio)
 if params.is_multi:
   keypings2 = lk.load_keypings(params.kp2_dir, params.parse_ratio)
   keypings.extend(keypings2)
 print("load keypings complated")

 # 初始化点云和场景信息
 init_PC, scene_info = spc.createPC(keypings, params.print_origPC, params.origPC_dir)
 print("init point cloud complated")

 # 进行射线近似
 render_masks = []
 ray_orig, ray_dir = [], []
 ray_opacity = []
 #keyping id ; keyping 
 for kid, kp in enumerate(keypings):
   render_masks.append(kp.mask)
   ro, rd = kp.get_rays()
   ray_orig.append(ro)
   ray_dir.append(rd)
   ray_opacity.append(render_masks[kid].flatten())
 
 ray_orig = np.concatenate(ray_orig, axis=0)
 ray_orig = (ray_orig - scene_info.center) / scene_info.scale # 射线起点归一化
 ray_dir = np.concatenate(ray_dir, axis=0)
 ray_opacity = np.concatenate(ray_opacity, axis=0)

 # 检测每一行是否包含 NaN 值
 rows_without_nan = ~np.isnan(ray_orig).any(axis=1)

 #更严谨的做法######
 # nan_mask = np.isnan(ray_orig).any(axis=1) | np.isnan(ray_dir).any(axis=1) | np.isnan(ray_opacity)
 # rows_without_nan = ~nan_mask
 #更严谨的做法######

 # 使用布尔索引过滤掉包含 NaN 的行
 ray_orig = ray_orig[rows_without_nan]
 ray_dir = ray_dir[rows_without_nan]
 ray_opacity = ray_opacity[rows_without_nan]

 ray_orig = torch.from_numpy(ray_orig).to("cuda").float()
 ray_dir = torch.from_numpy(ray_dir).to("cuda").float()
 ray_opacity = torch.from_numpy(ray_opacity).to("cuda").float()
 
 grid = fvdb.sparse_grid_from_points(
     init_PC,
     voxel_sizes=1.0 / (params.render_res - 1),
     origins=[0.0] * 3,
     mutable=True
 )

 # grid = fvdb.sparse_grid_from_dense(
 #   num_grids=1, dense_dims=[params.render_res] * 3,
 #   voxel_sizes=[1.0 / (params.render_res - 1)] * 3,
 #   origins=[0.0] * 3,
 #   mutable=True
 # )
 grid = grid.to("cuda")
 
 z_board = 5
 bbox_min = (grid.bbox[:, 0] - torch.tensor([5, 5, z_board]).long().to("cuda")).squeeze()
 bbox_max = (grid.bbox[:, 1] + torch.tensor([5, 5, z_board]).long().to("cuda")).squeeze()

 height_image0 = g2i.grid_to_height(grid, bbox_min, bbox_max, mod="mean") # 生成地底图

 # visualize_grid(grid) # 打印初始化网格
 grid = grid.coarsened_grid(4)
 # # visualize_grid(grid) # 打印降采样网格
 grid = grid.subdivided_grid(4)
 # visualize_grid(grid) # 打印升采样网格
 
 #---
