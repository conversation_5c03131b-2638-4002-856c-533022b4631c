# 体素网格空洞填充系统

## 概述

这是一个专门用于解决体素网格稀疏和空洞问题的独立渲染系统。与原始的 `render.py` 不同，这个系统集成了多种空洞填充算法，可以有效改善生成的体素网格质量。

## 文件结构

```
├── render_with_fill.py     # 主渲染程序（包含填充功能）
├── fill_config.py          # 填充参数配置文件
├── fill_utils.py           # 填充工具函数库
├── test_fill.py            # 测试脚本
└── README_FILL.md          # 本说明文档
```

## 快速开始

### 1. 基本使用

直接运行主程序：

```bash
python render_with_fill.py
```

程序会自动：
- 加载声纳数据
- 初始化体素网格
- 在训练过程中逐步填充空洞
- 训练结束后执行最终填充
- 生成填充后的高度图和点云

### 2. 配置填充参数

编辑 `fill_config.py` 文件来调整填充行为：

```python
# 修改当前配置类型
CURRENT_CONFIG = "balanced"  # 选项: "conservative", "aggressive", "balanced", "no_external_deps"
```

或者直接修改具体参数：

```python
SURFACE_FILL = {
    'enabled': True,        # 启用表面填充
    'depth': 2,            # 填充深度
    'threshold': 0.3,      # 透明度阈值
    'interval': 10,        # 执行间隔
}
```

### 3. 预设配置

系统提供4种预设配置：

- **conservative**: 保守填充，轻微改善，保持原始特征
- **balanced**: 平衡填充，效果和性能兼顾（默认）
- **aggressive**: 激进填充，强力消除空洞，可能过度填充
- **no_external_deps**: 无外部依赖，不使用OpenCV

## 填充算法

### 1. 表面向下填充
- **原理**: 识别表面体素，向下填充指定深度
- **适用**: 表面下方的小空洞
- **优点**: 快速、直观、内存友好
- **参数**: 深度、透明度阈值、执行间隔

### 2. 3D邻域填充
- **原理**: 为每个体素填充其26个邻域位置
- **适用**: 局部小空洞
- **优点**: 无外部依赖、简单有效
- **参数**: 迭代次数、透明度阈值

### 3. 垂直列填充
- **原理**: 按垂直列填充空隙
- **适用**: 层状结构、地形数据
- **优点**: 专门处理垂直方向空洞
- **参数**: 填充方向、最大间隙

### 4. 形态学填充
- **原理**: 基于图像处理的形态学操作
- **适用**: 复杂形状空洞
- **优点**: 成熟算法、效果好
- **依赖**: 需要 OpenCV
- **参数**: 核大小、迭代次数

## 性能优化

### 内存管理
- 填充会增加体素数量，注意GPU内存
- 定期执行 `torch.cuda.empty_cache()`
- 调整填充间隔以控制内存增长

### 速度优化
- 表面填充最快，优先使用
- 形态学填充较慢，谨慎使用
- 增加执行间隔可提高训练速度

### 参数调优
```python
# 快速配置（性能优先）
interval = 20          # 增加执行间隔
depth = 1             # 减少填充深度
threshold = 0.4       # 提高透明度阈值

# 质量配置（效果优先）
interval = 5          # 减少执行间隔
depth = 3             # 增加填充深度
threshold = 0.2       # 降低透明度阈值
```

## 测试和验证

### 运行测试
```bash
python test_fill.py
```

测试脚本会：
- 创建带空洞的测试网格
- 测试各种填充方法
- 输出填充前后的体素数量变化

### 查看配置
```bash
python fill_config.py
```

显示当前配置并进行验证。

### 可视化检查
程序会自动显示：
- 初始网格可视化
- 训练过程中的高度图
- 最终填充结果

## 故障排除

### 常见问题

**1. CUDA内存不足**
```
解决方案:
- 减少填充深度和间隙
- 增加执行间隔
- 使用保守配置
```

**2. 填充效果不明显**
```
解决方案:
- 降低透明度阈值
- 增加填充深度
- 使用激进配置
```

**3. 过度填充**
```
解决方案:
- 提高透明度阈值
- 减少填充深度
- 使用保守配置
```

**4. 形态学填充失败**
```
解决方案:
- 安装依赖: pip install opencv-python
- 或使用 no_external_deps 配置
```

### 依赖安装

基本依赖（必需）：
```bash
pip install torch fvdb numpy
```

可选依赖（形态学填充）：
```bash
pip install opencv-python scipy
```

## 配置示例

### 轻微填充（保守）
```python
CURRENT_CONFIG = "conservative"
```
适用于数据质量好、只需轻微改善的场景。

### 强力填充（激进）
```python
CURRENT_CONFIG = "aggressive"
```
适用于空洞较多、需要完整重建的场景。

### 自定义配置
```python
# 只使用表面填充
SURFACE_FILL['enabled'] = True
NEIGHBOR_FILL['enabled'] = False
VERTICAL_FILL['enabled'] = False
MORPHOLOGICAL_FILL['enabled'] = False
```

## 输出结果

程序会生成：
- 填充后的体素网格
- 改善的高度图
- 最终点云文件
- 训练日志和质量指标

## 与原版对比

| 特性 | render.py | render_with_fill.py |
|------|-----------|---------------------|
| 空洞填充 | ❌ | ✅ 4种算法 |
| 配置管理 | ❌ | ✅ 独立配置文件 |
| 预设模式 | ❌ | ✅ 4种预设 |
| 测试工具 | ❌ | ✅ 完整测试套件 |
| 文档说明 | ❌ | ✅ 详细文档 |

## 技术支持

如果遇到问题：
1. 检查依赖是否正确安装
2. 运行测试脚本验证功能
3. 查看配置是否合理
4. 检查GPU内存是否充足

## 更新日志

- v1.0: 初始版本，包含4种填充算法
- 支持配置文件管理
- 提供预设配置模式
- 完整的测试和文档
