#!/usr/bin/env python

import rospy
import cv2
import numpy as np
from sensor_msgs.msg import Image
from cv_bridge import CvBridge, CvBridgeError

class ImageConcatenator:
    def __init__(self):
        # 初始化ROS节点
        rospy.init_node('image_concatenator', anonymous=True)
        
        # 创建CV桥接器
        self.bridge = CvBridge()
        
        # 存储图像的列表
        self.images = []
        
        # 配置参数
        self.max_images = 200000  # 最大保存的图像数量，根据需要调整
        self.concat_direction = 'vertical'  # 'horizontal' 或 'vertical'
        self.save_interval = 800  # 每接收多少帧图像保存一次
        self.image_count = 0
        
        # 拼接结果
        self.concat_image = None
        
        # 订阅图像话题 /uuv1/img
        #self.image_sub = rospy.Subscriber('/img', Image, self.image_callback)
        self.image_sub = rospy.Subscriber('/uuv1/img', Image, self.image_callback)
        rospy.loginfo("图像拼接节点已启动，等待图像...")
    
    def image_callback(self, msg):
        """处理接收到的图像消息"""
        try:
            # 将ROS图像消息转换为OpenCV图像
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            
            # 存储图像
            self.images.append(cv_image)
            self.image_count += 1
            
            # 限制存储的图像数量
            if len(self.images) > self.max_images:
                self.images.pop(0)
            
            # 拼接图像
            self.concatenate_images()
            
            # 定期保存拼接结果
            if self.image_count % self.save_interval == 0 and self.concat_image is not None:
                timestamp = rospy.Time.now().to_sec()
                filename = f"concatenated_{timestamp:.0f}.jpg"
                cv2.imwrite(filename, self.concat_image)
                rospy.loginfo(f"拼接图像已保存为 {filename}")
            
        except CvBridgeError as e:
            rospy.logerr("CvBridge转换错误: %s", e)
    
    def concatenate_images(self):
        """将所有图像拼接在一起"""
        if not self.images:
            return
        
        # 确保所有图像具有相同的尺寸
        h, w = self.images[0].shape[:2]
        resized_images = []
        
        for img in self.images:
            if img.shape[:2] != (h, w):
                img_resized = cv2.resize(img, (w, h))
                resized_images.append(img_resized)
            else:
                resized_images.append(img)
        
        # 根据方向拼接图像
        if self.concat_direction == 'horizontal':
            self.concat_image = np.hstack(resized_images)
        else:  # vertical - 第一张图片在下面往上拼接
            self.concat_image = np.vstack(resized_images[::-1])
        
        # 显示拼接结果
        cv2.namedWindow('Concatenated Images', cv2.WINDOW_NORMAL)
        cv2.imshow('Concatenated Images', self.concat_image)
        cv2.waitKey(1)
    
    def run(self):
        """运行节点，直到被关闭"""
        rospy.spin()
        
        # 关闭所有窗口
        cv2.destroyAllWindows()
        
        # 保存最终拼接结果
        if self.concat_image is not None:
            final_filename = f"final_concatenated_{rospy.Time.now().to_sec():.0f}.jpg"
            cv2.imwrite(final_filename, self.concat_image)
            rospy.loginfo(f"最终拼接图像已保存为 {final_filename}")

if __name__ == "__main__":
    try:
        concatenator = ImageConcatenator()
        concatenator.run()
    except rospy.ROSInterruptException:
        pass

