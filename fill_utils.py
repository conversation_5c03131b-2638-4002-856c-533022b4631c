"""
体素网格空洞填充工具函数
提供独立的填充功能，可以在其他脚本中使用
"""
import torch
import numpy as np
from fvdb import JaggedTensor, GridBatch
import math

def inv_sigmoid(x):
    """将透明度值转换到sigmoid参数空间"""
    return -math.log(1 / x - 1)

def fill_surface_downward_simple(grid: GridBatch, alpha: torch.Tensor, fill_depth=3, min_alpha_threshold=0.3):
    """
    简化版表面向下填充
    Args:
        grid: FVDB网格
        alpha: 透明度参数
        fill_depth: 向下填充的深度（体素单位）
        min_alpha_threshold: 被认为是表面的最小透明度阈值
    Returns:
        填充后的网格和透明度参数
    """
    print(f"执行简化版表面向下填充，填充深度: {fill_depth}")
    
    with torch.no_grad():
        if grid.total_voxels == 0:
            print("网格为空，跳过填充")
            return grid, alpha
            
        # 获取当前启用的体素坐标
        current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
        current_alpha_values = torch.sigmoid(alpha[grid.enabled_mask[0].jdata])
        
        # 找到表面体素（透明度高于阈值的体素）
        surface_mask = current_alpha_values >= min_alpha_threshold
        surface_coords = current_coords[surface_mask]
        
        if len(surface_coords) == 0:
            print("未找到表面体素，跳过填充")
            return grid, alpha
            
        print(f"找到 {len(surface_coords)} 个表面体素")
        
        # 创建现有坐标的集合以快速查找
        existing_coords_set = set()
        for coord in current_coords:
            coord_tuple = tuple(coord.cpu().numpy())
            existing_coords_set.add(coord_tuple)
        
        # 为每个表面体素生成向下填充的坐标
        fill_coords_list = []
        fill_alpha_list = []
        
        for coord in surface_coords:
            for depth in range(1, fill_depth + 1):
                # 向下填充（z坐标减小）
                fill_coord = coord.clone()
                fill_coord[2] -= depth
                
                # 检查是否已经存在该体素
                coord_tuple = tuple(fill_coord.cpu().numpy())
                if coord_tuple not in existing_coords_set:
                    fill_coords_list.append(fill_coord)
                    existing_coords_set.add(coord_tuple)  # 避免重复添加
                    
                    # 随深度递减的透明度
                    decay_factor = max(0.1, 1.0 - (depth * 0.2))
                    fill_alpha_value = min_alpha_threshold * decay_factor
                    fill_alpha_list.append(inv_sigmoid(fill_alpha_value))
        
        # 添加新的填充体素
        if fill_coords_list:
            fill_coords = torch.stack(fill_coords_list)
            fill_alpha_values = torch.tensor(fill_alpha_list, device=grid.device, requires_grad=True)
            
            print(f"添加 {len(fill_coords)} 个填充体素")
            
            # 启用新体素
            grid.enable_ijk(JaggedTensor([fill_coords]))
            
            # 扩展alpha参数
            alpha.data = torch.cat([alpha.data, fill_alpha_values])
            
        else:
            print("未生成新的填充体素")
    
    return grid, alpha

def fill_neighbors_3d(grid: GridBatch, alpha: torch.Tensor, iterations=1, min_alpha_threshold=0.2):
    """
    3D邻域填充 - 不依赖外部库的简单填充方法
    Args:
        grid: FVDB网格
        alpha: 透明度参数
        iterations: 填充迭代次数
        min_alpha_threshold: 新体素的透明度阈值
    Returns:
        填充后的网格和透明度参数
    """
    print(f"执行3D邻域填充，迭代次数: {iterations}")
    
    with torch.no_grad():
        if grid.total_voxels == 0:
            print("网格为空，跳过填充")
            return grid, alpha
        
        # 26邻域偏移（3D）
        neighbor_offsets = []
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                for dz in [-1, 0, 1]:
                    if dx == 0 and dy == 0 and dz == 0:
                        continue
                    neighbor_offsets.append([dx, dy, dz])
        
        neighbor_offsets = torch.tensor(neighbor_offsets, device=grid.device)
        
        for iteration in range(iterations):
            print(f"  迭代 {iteration + 1}/{iterations}")
            
            # 获取当前启用的体素坐标
            current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
            
            if len(current_coords) == 0:
                break
                
            # 创建现有坐标的集合
            existing_coords_set = set()
            for coord in current_coords:
                coord_tuple = tuple(coord.cpu().numpy())
                existing_coords_set.add(coord_tuple)
            
            # 为每个现有体素检查其邻域
            new_coords_list = []
            
            for coord in current_coords:
                # 检查所有26个邻域位置
                for offset in neighbor_offsets:
                    neighbor_coord = coord + offset
                    coord_tuple = tuple(neighbor_coord.cpu().numpy())
                    
                    # 如果邻域位置不存在体素，则添加
                    if coord_tuple not in existing_coords_set:
                        new_coords_list.append(neighbor_coord)
                        existing_coords_set.add(coord_tuple)  # 避免重复添加
            
            # 添加新体素
            if new_coords_list:
                new_coords = torch.stack(new_coords_list)
                print(f"    添加 {len(new_coords)} 个邻域体素")
                
                # 启用新体素
                grid.enable_ijk(JaggedTensor([new_coords]))
                
                # 为新体素设置透明度
                new_alpha = torch.full(
                    (len(new_coords),), 
                    inv_sigmoid(min_alpha_threshold),
                    device=grid.device, 
                    requires_grad=True
                )
                alpha.data = torch.cat([alpha.data, new_alpha])
            else:
                print(f"    第{iteration + 1}次迭代未添加新体素")
                break
    
    return grid, alpha

def fill_vertical_columns(grid: GridBatch, alpha: torch.Tensor, fill_below=True, fill_above=False, 
                         max_gap=3, min_alpha_threshold=0.25):
    """
    垂直列填充 - 填充垂直方向上的空隙
    Args:
        grid: FVDB网格
        alpha: 透明度参数
        fill_below: 是否向下填充
        fill_above: 是否向上填充
        max_gap: 最大填充间隙
        min_alpha_threshold: 新体素的透明度阈值
    Returns:
        填充后的网格和透明度参数
    """
    print(f"执行垂直列填充，向下: {fill_below}, 向上: {fill_above}, 最大间隙: {max_gap}")
    
    with torch.no_grad():
        if grid.total_voxels == 0:
            print("网格为空，跳过填充")
            return grid, alpha
            
        # 获取当前启用的体素坐标
        current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
        
        # 按(x,y)分组，找到每列的z值
        xy_groups = {}
        for coord in current_coords:
            x, y, z = coord.cpu().numpy()
            key = (int(x), int(y))
            if key not in xy_groups:
                xy_groups[key] = []
            xy_groups[key].append(int(z))
        
        # 创建现有坐标的集合
        existing_coords_set = set()
        for coord in current_coords:
            coord_tuple = tuple(coord.cpu().numpy())
            existing_coords_set.add(coord_tuple)
        
        new_coords_list = []
        
        # 对每个(x,y)列进行填充
        for (x, y), z_list in xy_groups.items():
            z_list = sorted(z_list)
            
            if fill_below and len(z_list) > 0:
                # 向下填充到最低点
                min_z = min(z_list)
                for z in range(min_z - max_gap, min_z):
                    coord_tuple = (x, y, z)
                    if coord_tuple not in existing_coords_set:
                        new_coord = torch.tensor([x, y, z], device=grid.device)
                        new_coords_list.append(new_coord)
                        existing_coords_set.add(coord_tuple)
            
            if fill_above and len(z_list) > 0:
                # 向上填充到最高点
                max_z = max(z_list)
                for z in range(max_z + 1, max_z + max_gap + 1):
                    coord_tuple = (x, y, z)
                    if coord_tuple not in existing_coords_set:
                        new_coord = torch.tensor([x, y, z], device=grid.device)
                        new_coords_list.append(new_coord)
                        existing_coords_set.add(coord_tuple)
            
            # 填充列内的间隙
            if len(z_list) > 1:
                for i in range(len(z_list) - 1):
                    z_start = z_list[i]
                    z_end = z_list[i + 1]
                    gap_size = z_end - z_start - 1
                    
                    if 0 < gap_size <= max_gap:
                        for z in range(z_start + 1, z_end):
                            coord_tuple = (x, y, z)
                            if coord_tuple not in existing_coords_set:
                                new_coord = torch.tensor([x, y, z], device=grid.device)
                                new_coords_list.append(new_coord)
                                existing_coords_set.add(coord_tuple)
        
        # 添加新体素
        if new_coords_list:
            new_coords = torch.stack(new_coords_list)
            print(f"添加 {len(new_coords)} 个垂直填充体素")
            
            # 启用新体素
            grid.enable_ijk(JaggedTensor([new_coords]))
            
            # 为新体素设置透明度
            new_alpha = torch.full(
                (len(new_coords),), 
                inv_sigmoid(min_alpha_threshold),
                device=grid.device, 
                requires_grad=True
            )
            alpha.data = torch.cat([alpha.data, new_alpha])
        else:
            print("未生成新的垂直填充体素")
    
    return grid, alpha
