import torch
import fvdb
import math
import time

import numpy as np
import src.loadKeypings as lk
import src.scenePC as spc
import src.grid2image as g2i
import src.confidence_gaussian as cg
import src.params as params
import polyscope as ps
import point_cloud_utils as pcu
from fvdb import JaggedTensor, GridBatch

from torch.utils.tensorboard import SummaryWriter

#将初始透明度值转换到合适的参数空间，便于梯度优化
def inv_sigmoid(x):
    return -math.log(1 / x - 1)

def simple_fill_to_seafloor(grid: GridBatch, seafloor_offset=5):
    """
    简单有效的海底填充方法
    从每个现有体素向下填充到海底平面
    Args:
        grid: FVDB网格
        seafloor_offset: 海底平面相对于最低点的偏移量
    Returns:
        填充后的网格
    """
    print("执行简单的海底填充...")
    
    if grid.total_voxels == 0:
        print("网格为空，跳过填充")
        return grid
        
    # 获取当前所有启用的体素坐标
    current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
    
    if len(current_coords) == 0:
        print("没有启用的体素，跳过填充")
        return grid
    
    # 计算海底高度：最低点再向下偏移
    seafloor_z = current_coords[:, 2].min().item() - seafloor_offset
    print(f"海底高度设定为: {seafloor_z} (最低点 - {seafloor_offset})")
    
    # 创建现有坐标的集合以快速查找
    existing_coords_set = set()
    for coord in current_coords:
        coord_tuple = tuple(coord.cpu().numpy())
        existing_coords_set.add(coord_tuple)
    
    # 按(x,y)分组，找到每列的体素
    xy_groups = {}
    for coord in current_coords:
        x, y, z = coord.cpu().numpy()
        key = (int(x), int(y))
        if key not in xy_groups:
            xy_groups[key] = []
        xy_groups[key].append(int(z))
    
    print(f"找到 {len(xy_groups)} 个不同的(x,y)列")
    
    # 为每个(x,y)列向下填充到海底
    fill_coords_list = []
    
    for (x, y), z_list in xy_groups.items():
        # 找到该列的最低点
        min_z = min(z_list)
        
        # 从最低点向下填充到海底
        for z in range(min_z - 1, int(seafloor_z) - 1, -1):
            coord_tuple = (x, y, z)
            if coord_tuple not in existing_coords_set:
                new_coord = torch.tensor([x, y, z], device=grid.device)
                fill_coords_list.append(new_coord)
                existing_coords_set.add(coord_tuple)
    
    # 添加新的填充体素
    if fill_coords_list:
        fill_coords = torch.stack(fill_coords_list)
        print(f"向下填充到海底，添加 {len(fill_coords)} 个体素")
        
        # 启用新体素
        grid.enable_ijk(JaggedTensor([fill_coords]))
        
        print(f"填充完成！体素数量: {grid.total_voxels}")
    else:
        print("未生成新的填充体素（可能已经填充到海底）")
    
    return grid

def visualize_grid_color(grid: GridBatch, rgb: JaggedTensor, ignore_disabled: bool = False):
    for b in range(grid.grid_count):
        grid_mask = grid.enabled_mask[b].jdata.cpu().numpy()
        if ignore_disabled:
            grid_mask.fill(True)

        grid_mesh = pcu.voxel_grid_geometry(
            grid.ijk[b].jdata.cpu().numpy()[grid_mask],
            0.1,
            grid.voxel_sizes[b].cpu().numpy())
        grid_color = rgb[b].jdata.cpu().numpy()[grid_mask].repeat(8, axis=0).reshape(-1, 3)

        ps.register_surface_mesh(
            f"grid_{b}", grid_mesh[0], grid_mesh[1], enabled=True
        ).add_color_quantity("color", grid_color, enabled=True)

def visualize_grid(grid: GridBatch):
  ps.init()
  
  feature = grid.grid_to_world(grid.ijk.float())
  z_values = feature.jdata[:, 2]

  z_min = z_values.min()
  z_max = z_values.max()
  z_values = (z_values - z_min) / (z_max - z_min)
  
  # 创建颜色数组
  colors = torch.zeros_like(feature.jdata)
  
  # 最低平面设置为白色 (z值小于0.3的部分)
  bottom_mask = z_values < 0.3
  above_mask = z_values >= 0.3
  grid.disable_ijk(grid.ijk.r_masked_select(bottom_mask))
  # 最低平面设为白色
  colors[bottom_mask] = 1.0  # RGB都设为1，呈现白色
  
  # 高于底部的部分处理
  above_indices = torch.where(above_mask)[0]
  above_z_values = z_values[above_indices]
  
  # 重新归一化到[0,1]
  normalized_z = (above_z_values - 0.3) / 0.7
  
  # 蓝色到绿色 (0 <= normalized_z <= 0.5)
  blue_to_green_indices = above_indices[normalized_z <= 0.5]
  blue_to_green_z = normalized_z[normalized_z <= 0.5]
  
  colors[blue_to_green_indices, 0] = 0  # 红色分量为0
  colors[blue_to_green_indices, 1] = blue_to_green_z * 2  # 绿色分量从0增加到1
  colors[blue_to_green_indices, 2] = 1 - blue_to_green_z * 2  # 蓝色分量从1减少到0
  
  # 绿色到红色 (0.5 < normalized_z <= 1.0)
  green_to_red_indices = above_indices[normalized_z > 0.5]
  green_to_red_z = normalized_z[normalized_z > 0.5]
  
  colors[green_to_red_indices, 0] = (green_to_red_z - 0.5) * 2  # 红色分量从0增加到1
  colors[green_to_red_indices, 1] = 1 - (green_to_red_z - 0.5) * 2  # 绿色分量从1减少到0
  colors[green_to_red_indices, 2] = 0  # 蓝色分量为0

  # 将颜色赋值给feature
  feature.jdata = colors

  # Visualization
  ps.remove_all_structures()
  visualize_grid_color(grid, feature)
  ps.set_ground_plane_mode("none")
  ps.show()

def render_opacity(grid: fvdb.GridBatch, feature: torch.Tensor, ray_orig=None, ray_dir=None):
    #确保光线起点和方向参数已提供
    assert ray_orig is not None and ray_dir is not None
    if torch.isnan(feature).any():
      print("feature contains NaN")#检查 feature 张量中是否存在 NaN（非数值）
      print("feature:", feature)

    pack_info, voxel_inds, out_times = grid.voxels_along_rays(ray_orig, ray_dir, 128, 0.0)
    pack_info = pack_info.jdata
    out_times = out_times.jdata
    voxel_inds = grid.ijk_to_index(voxel_inds).jdata

    _, _, opacity, _, _ = fvdb.utils.volume_render(
        sigmas=-torch.log(1 - feature[voxel_inds]),
        rgbs=torch.ones((voxel_inds.shape[0], 1), device=grid.device),
        deltaTs=torch.ones(voxel_inds.shape[0], device=grid.device),
        ts=out_times.mean(1),
        packInfo=pack_info, transmittanceThresh=0.0
    )
    return opacity

if __name__ == "__main__":
    print("=== 简单海底填充版本 ===")
    print("这个版本使用最简单直接的方法：")
    print("- 从每个体素向下填充到海底平面")
    print("- 按高度决定颜色")
    print("- 避免复杂的训练中填充")
    print("- 符合物理直觉，简单可靠")
    print()

    # 读取声纳回波数据
    keypings = lk.load_keypings(params.kp_dir, params.parse_ratio)
    if params.is_multi:
        keypings2 = lk.load_keypings(params.kp2_dir, params.parse_ratio)
        keypings.extend(keypings2)
    print("load keypings complated")

    # 初始化点云和场景信息
    init_PC, scene_info = spc.createPC(keypings, params.print_origPC, params.origPC_dir)
    print("init point cloud complated")

    # 进行射线近似
    render_masks = []
    ray_orig, ray_dir = [], []
    ray_opacity = []
    #keyping id ; keyping 
    for kid, kp in enumerate(keypings):
        render_masks.append(kp.mask)
        ro, rd = kp.get_rays()
        ray_orig.append(ro)
        ray_dir.append(rd)
        ray_opacity.append(render_masks[kid].flatten())
    
    ray_orig = np.concatenate(ray_orig, axis=0)
    ray_orig = (ray_orig - scene_info.center) / scene_info.scale # 射线起点归一化
    ray_dir = np.concatenate(ray_dir, axis=0)
    ray_opacity = np.concatenate(ray_opacity, axis=0)

    # 检测每一行是否包含 NaN 值
    rows_without_nan = ~np.isnan(ray_orig).any(axis=1)

    # 使用布尔索引过滤掉包含 NaN 的行
    ray_orig = ray_orig[rows_without_nan]
    ray_dir = ray_dir[rows_without_nan]
    ray_opacity = ray_opacity[rows_without_nan]

    ray_orig = torch.from_numpy(ray_orig).to("cuda").float()
    ray_dir = torch.from_numpy(ray_dir).to("cuda").float()
    ray_opacity = torch.from_numpy(ray_opacity).to("cuda").float()
    
    grid = fvdb.sparse_grid_from_points(
        init_PC,
        voxel_sizes=1.0 / (params.render_res - 1),
        origins=[0.0] * 3,
        mutable=True
    )
    grid = grid.to("cuda")
    
    z_board = 5
    bbox_min = (grid.bbox[:, 0] - torch.tensor([5, 5, z_board]).long().to("cuda")).squeeze()
    bbox_max = (grid.bbox[:, 1] + torch.tensor([5, 5, z_board]).long().to("cuda")).squeeze()

    height_image0 = g2i.grid_to_height(grid, bbox_min, bbox_max, mod="mean") # 生成地底图

    # visualize_grid(grid) # 打印初始化网格
    grid = grid.coarsened_grid(4)
    # # visualize_grid(grid) # 打印降采样网格
    grid = grid.subdivided_grid(4)
    # visualize_grid(grid) # 打印升采样网格
    
    print(f"初始网格体素数量: {grid.total_voxels}")
    
    #---------------------------------------------------
    
    alpha = torch.full((grid.total_voxels, ), inv_sigmoid(params.init_alpha), device=grid.device, requires_grad=True)

    if torch.isnan(alpha).any():
        print("alpha contains NaN")
        print("alpha:", alpha)

    optimizer = torch.optim.Adam([alpha], lr=1.0)
    merge_images = []
    writer = SummaryWriter(log_dir=params.log_dir)  # 日志保存路径
    
    # 在训练循环前记录开始时间
    print(f"当前显存占用: {torch.cuda.memory_allocated() / 1024**2:.2f} MB")
    total_start = time.time()
    
    print("\n=== 开始训练循环（无中间填充） ===")
    for it in range(100):
        
        sample_num = params.sample_num
        if params.random_sample:
            # 随机索引
            sub_inds = torch.randint(0, ray_orig.shape[0], (sample_num, ), device=grid.device)
        else:
            # 按顺序索引
            sub_inds = torch.arange(it*sample_num, (it+1)*sample_num) % ray_orig.shape[0]
            sub_inds = sub_inds.to(grid.device)

        pd_opacity = render_opacity(grid, torch.sigmoid(alpha), ray_orig=ray_orig[sub_inds], ray_dir=ray_dir[sub_inds])
        gt_opacity = ray_opacity[sub_inds]
        # 检查 pd_opacity 是否包含 NaN
        pd_opacity = torch.nan_to_num(pd_opacity, nan=0.0)
        loss = torch.mean(torch.abs(pd_opacity - gt_opacity))
        print("Iter", it, "Loss:", loss.item(), f"Voxels: {grid.total_voxels}")
        writer.add_scalar('Loss/train', loss.item(), it)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        alpha.data = torch.where(torch.isnan(alpha), torch.zeros_like(alpha), alpha)
        
        if it > 0 and it % 5 == 0:
            torch.cuda.empty_cache()
            with torch.no_grad():
                # disable
                bad_mask = torch.sigmoid(alpha) < 0.1
                grid.disable_ijk(grid.ijk.r_masked_select(bad_mask))

                # random revive
                if it < 20:
                    enable_mask = torch.rand(grid.total_voxels, device=grid.device) < 0.01
                    grid.enable_ijk(grid.ijk.r_masked_select(enable_mask))

                height_image1 = g2i.grid_to_height(grid, bbox_min, bbox_max)
                merge_image = g2i.merge_height(height_image0, height_image1)
                merge_images.append(merge_image)

    writer.close()
    
    print(f"训练完成时网格体素数量: {grid.total_voxels}")
    
    # ===== 执行简单的海底填充 =====
    print("\n=== 执行简单海底填充 ===")
    
    # 显示填充前的网格
    print("填充前的网格可视化...")
    # visualize_grid(grid)  # 可以取消注释查看填充前的效果
    
    # 执行简单填充
    grid = simple_fill_to_seafloor(grid, seafloor_offset=5)
    
    print(f"填充后网格体素数量: {grid.total_voxels}")
    
    # 显示填充后的网格
    print("填充后的网格可视化...")
    visualize_grid(grid)
    
    # 重新生成高度图（因为网格已经改变）
    height_image_after_fill = g2i.grid_to_height(grid, bbox_min, bbox_max)
    
    # 显示各阶段的高度图
    print("显示高度图对比...")
    
    # 初始化高度图
    print("1. 初始高度图")
    g2i.show_images(height_image0)
    
    # 训练后的高度图
    print("2. 训练后高度图")
    height_image1 = merge_images[-1] if merge_images else height_image0
    g2i.show_images(height_image1)
    
    # 填充后的高度图
    print("3. 填充后高度图")
    g2i.show_images(height_image_after_fill)
    
    # 合并高度图
    final_image = g2i.merge_height(height_image0, height_image_after_fill)
    print("4. 最终合并高度图")
    g2i.show_images(final_image)

    # 应用高斯卷积 - 动态平滑模块
    use_smooth = params.use_smooth if hasattr(params, 'use_smooth') else True
    
    if use_smooth:
        final_image = cg.confidence_gaussian(final_image, kernel_size = 11, sigma = 0.75, iter = 3)
        print("Applied dynamic smoothing")
        print("5. 平滑后高度图")
        g2i.show_images(final_image)

    z_scale = (bbox_max-bbox_min)[2] - z_board * 2
    # 打印最终的稠密点云
    pc = spc.image_to_pc(final_image[0], grid.voxel_sizes, z_scale)
    if params.print_finalPC:
        spc.print_pointcloud(pc, params.final_pc_dir)

    # 从最终点云重新创建网格用于最终可视化
    final_grid = fvdb.sparse_grid_from_points(
        pc,
        voxel_sizes=1.0 / (params.render_res - 1),
        origins=[0.0] * 3,
        mutable=True
    )

    print("最终网格可视化...")
    visualize_grid(final_grid)
    
    print("\n=== 简单海底填充完成 ===")
    print(f"最终体素数量: {final_grid.total_voxels}")
    print("填充方法: 简单向下填充到海底平面")
    print("特点: 简单、直接、符合物理直觉")
    print("优势: 无梯度问题、计算高效、结果可靠")
    print("="*50)
