import torch
import fvdb
import math
import time

import numpy as np
import src.loadKeypings as lk
import src.scenePC as spc
import src.grid2image as g2i
import src.confidence_gaussian as cg
import src.params as params
import polyscope as ps
import point_cloud_utils as pcu
from fvdb import JaggedTensor, GridBatch

from torch.utils.tensorboard import SummaryWriter

#将初始透明度值转换到合适的参数空间，便于梯度优化
def inv_sigmoid(x):
    return -math.log(1 / x - 1)

def fill_surface_downward(grid: GridBatch, alpha: torch.Tensor, fill_depth=3, min_alpha_threshold=0.3):
    """
    将表面网格向下填充以消除空洞
    Args:
        grid: FVDB网格
        alpha: 透明度参数
        fill_depth: 向下填充的深度（体素单位）
        min_alpha_threshold: 被认为是表面的最小透明度阈值
    Returns:
        填充后的网格和透明度参数
    """
    print(f"开始表面向下填充，填充深度: {fill_depth}")
    
    with torch.no_grad():
        # 获取当前启用的体素坐标
        if grid.total_voxels == 0:
            print("网格为空，跳过填充")
            return grid, alpha
            
        current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
        current_alpha_values = torch.sigmoid(alpha[grid.enabled_mask[0].jdata])
        
        # 找到表面体素（透明度高于阈值的体素）
        surface_mask = current_alpha_values >= min_alpha_threshold
        surface_coords = current_coords[surface_mask]
        
        if len(surface_coords) == 0:
            print("未找到表面体素，跳过填充")
            return grid, alpha
            
        print(f"找到 {len(surface_coords)} 个表面体素")
        
        # 创建现有坐标的集合以快速查找
        existing_coords_set = set()
        for coord in current_coords:
            coord_tuple = tuple(coord.cpu().numpy())
            existing_coords_set.add(coord_tuple)
        
        # 为每个表面体素生成向下填充的坐标
        fill_coords_list = []
        fill_alpha_list = []
        
        for coord in surface_coords:
            for depth in range(1, fill_depth + 1):
                # 向下填充（z坐标减小）
                fill_coord = coord.clone()
                fill_coord[2] -= depth
                
                # 检查是否已经存在该体素
                coord_tuple = tuple(fill_coord.cpu().numpy())
                if coord_tuple not in existing_coords_set:
                    fill_coords_list.append(fill_coord)
                    existing_coords_set.add(coord_tuple)  # 避免重复添加
                    
                    # 随深度递减的透明度
                    decay_factor = max(0.1, 1.0 - (depth * 0.2))
                    fill_alpha_value = min_alpha_threshold * decay_factor
                    fill_alpha_list.append(inv_sigmoid(fill_alpha_value))
        
        # 添加新的填充体素
        if fill_coords_list:
            fill_coords = torch.stack(fill_coords_list)
            fill_alpha_values = torch.tensor(fill_alpha_list, device=grid.device)

            print(f"添加 {len(fill_coords)} 个填充体素")

            # 启用新体素
            grid.enable_ijk(JaggedTensor([fill_coords]))

            # 扩展alpha参数 - 创建新的tensor而不是修改原有的
            new_alpha = torch.cat([alpha.data.detach(), fill_alpha_values])
            new_alpha.requires_grad_(True)
            alpha.data = new_alpha
            alpha.grad = None  # 清除旧的梯度

        else:
            print("未生成新的填充体素")
    
    return grid, alpha

def fill_neighbors_3d(grid: GridBatch, alpha: torch.Tensor, iterations=1, min_alpha_threshold=0.2):
    """
    3D邻域填充 - 填充现有体素的26邻域
    Args:
        grid: FVDB网格
        alpha: 透明度参数
        iterations: 填充迭代次数
        min_alpha_threshold: 新体素的透明度阈值
    Returns:
        填充后的网格和透明度参数
    """
    print(f"执行3D邻域填充，迭代次数: {iterations}")
    
    with torch.no_grad():
        if grid.total_voxels == 0:
            print("网格为空，跳过填充")
            return grid, alpha
        
        # 26邻域偏移（3D）
        neighbor_offsets = []
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                for dz in [-1, 0, 1]:
                    if dx == 0 and dy == 0 and dz == 0:
                        continue
                    neighbor_offsets.append([dx, dy, dz])
        
        neighbor_offsets = torch.tensor(neighbor_offsets, device=grid.device)
        
        for iteration in range(iterations):
            print(f"  迭代 {iteration + 1}/{iterations}")
            
            # 获取当前启用的体素坐标
            current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
            
            if len(current_coords) == 0:
                break
                
            # 创建现有坐标的集合
            existing_coords_set = set()
            for coord in current_coords:
                coord_tuple = tuple(coord.cpu().numpy())
                existing_coords_set.add(coord_tuple)
            
            # 为每个现有体素检查其邻域
            new_coords_list = []
            
            for coord in current_coords:
                # 检查所有26个邻域位置
                for offset in neighbor_offsets:
                    neighbor_coord = coord + offset
                    coord_tuple = tuple(neighbor_coord.cpu().numpy())
                    
                    # 如果邻域位置不存在体素，则添加
                    if coord_tuple not in existing_coords_set:
                        new_coords_list.append(neighbor_coord)
                        existing_coords_set.add(coord_tuple)  # 避免重复添加
            
            # 添加新体素
            if new_coords_list:
                new_coords = torch.stack(new_coords_list)
                print(f"    添加 {len(new_coords)} 个邻域体素")

                # 启用新体素
                grid.enable_ijk(JaggedTensor([new_coords]))

                # 为新体素设置透明度
                new_alpha_values = torch.full(
                    (len(new_coords),),
                    inv_sigmoid(min_alpha_threshold),
                    device=grid.device
                )

                # 扩展alpha参数
                combined_alpha = torch.cat([alpha.data.detach(), new_alpha_values])
                combined_alpha.requires_grad_(True)
                alpha.data = combined_alpha
                alpha.grad = None
            else:
                print(f"    第{iteration + 1}次迭代未添加新体素")
                break
    
    return grid, alpha

def fill_vertical_columns(grid: GridBatch, alpha: torch.Tensor, fill_below=True, fill_above=False, 
                         max_gap=3, min_alpha_threshold=0.25):
    """
    垂直列填充 - 填充垂直方向上的空隙
    Args:
        grid: FVDB网格
        alpha: 透明度参数
        fill_below: 是否向下填充
        fill_above: 是否向上填充
        max_gap: 最大填充间隙
        min_alpha_threshold: 新体素的透明度阈值
    Returns:
        填充后的网格和透明度参数
    """
    print(f"执行垂直列填充，向下: {fill_below}, 向上: {fill_above}, 最大间隙: {max_gap}")
    
    with torch.no_grad():
        if grid.total_voxels == 0:
            print("网格为空，跳过填充")
            return grid, alpha
            
        # 获取当前启用的体素坐标
        current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
        
        # 按(x,y)分组，找到每列的z值
        xy_groups = {}
        for coord in current_coords:
            x, y, z = coord.cpu().numpy()
            key = (int(x), int(y))
            if key not in xy_groups:
                xy_groups[key] = []
            xy_groups[key].append(int(z))
        
        # 创建现有坐标的集合
        existing_coords_set = set()
        for coord in current_coords:
            coord_tuple = tuple(coord.cpu().numpy())
            existing_coords_set.add(coord_tuple)
        
        new_coords_list = []
        
        # 对每个(x,y)列进行填充
        for (x, y), z_list in xy_groups.items():
            z_list = sorted(z_list)
            
            if fill_below and len(z_list) > 0:
                # 向下填充到最低点
                min_z = min(z_list)
                for z in range(min_z - max_gap, min_z):
                    coord_tuple = (x, y, z)
                    if coord_tuple not in existing_coords_set:
                        new_coord = torch.tensor([x, y, z], device=grid.device)
                        new_coords_list.append(new_coord)
                        existing_coords_set.add(coord_tuple)
            
            if fill_above and len(z_list) > 0:
                # 向上填充到最高点
                max_z = max(z_list)
                for z in range(max_z + 1, max_z + max_gap + 1):
                    coord_tuple = (x, y, z)
                    if coord_tuple not in existing_coords_set:
                        new_coord = torch.tensor([x, y, z], device=grid.device)
                        new_coords_list.append(new_coord)
                        existing_coords_set.add(coord_tuple)
            
            # 填充列内的间隙
            if len(z_list) > 1:
                for i in range(len(z_list) - 1):
                    z_start = z_list[i]
                    z_end = z_list[i + 1]
                    gap_size = z_end - z_start - 1
                    
                    if 0 < gap_size <= max_gap:
                        for z in range(z_start + 1, z_end):
                            coord_tuple = (x, y, z)
                            if coord_tuple not in existing_coords_set:
                                new_coord = torch.tensor([x, y, z], device=grid.device)
                                new_coords_list.append(new_coord)
                                existing_coords_set.add(coord_tuple)
        
        # 添加新体素
        if new_coords_list:
            new_coords = torch.stack(new_coords_list)
            print(f"添加 {len(new_coords)} 个垂直填充体素")

            # 启用新体素
            grid.enable_ijk(JaggedTensor([new_coords]))

            # 为新体素设置透明度
            new_alpha_values = torch.full(
                (len(new_coords),),
                inv_sigmoid(min_alpha_threshold),
                device=grid.device
            )

            # 扩展alpha参数
            combined_alpha = torch.cat([alpha.data.detach(), new_alpha_values])
            combined_alpha.requires_grad_(True)
            alpha.data = combined_alpha
            alpha.grad = None
        else:
            print("未生成新的垂直填充体素")
    
    return grid, alpha

def fill_holes_morphological(grid: GridBatch, alpha: torch.Tensor, bbox_min, bbox_max,
                           kernel_size=3, iterations=2):
    """
    使用形态学操作填充空洞
    Args:
        grid: FVDB网格
        alpha: 透明度参数
        bbox_min, bbox_max: 边界框
        kernel_size: 形态学操作核大小
        iterations: 迭代次数
    """
    print(f"使用形态学操作填充空洞，核大小: {kernel_size}, 迭代次数: {iterations}")

    try:
        import cv2
    except ImportError:
        print("需要安装 opencv-python 来使用形态学填充")
        return grid, alpha

    with torch.no_grad():
        # 生成当前高度图
        current_height = g2i.grid_to_height(grid, bbox_min, bbox_max, mod="mean")
        height_data = current_height[0].cpu().numpy()

        # 创建二值掩码（有高度的区域为1，空洞为0）
        binary_mask = (height_data > 0.001).astype(np.uint8)

        # 形态学闭运算填充空洞
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        filled_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel, iterations=iterations)

        # 找到新填充的区域
        new_fill_mask = (filled_mask == 1) & (binary_mask == 0)

        if np.any(new_fill_mask):
            # 对新填充区域进行高度插值
            filled_height = height_data.copy()

            # 使用周围有效像素的平均高度
            for i in range(height_data.shape[0]):
                for j in range(height_data.shape[1]):
                    if new_fill_mask[i, j]:
                        # 在邻域内寻找有效高度值
                        neighbors = []
                        for di in range(-kernel_size//2, kernel_size//2 + 1):
                            for dj in range(-kernel_size//2, kernel_size//2 + 1):
                                ni, nj = i + di, j + dj
                                if (0 <= ni < height_data.shape[0] and
                                    0 <= nj < height_data.shape[1] and
                                    height_data[ni, nj] > 0.001):
                                    neighbors.append(height_data[ni, nj])

                        if neighbors:
                            filled_height[i, j] = np.mean(neighbors)

            # 将填充后的高度图转换回体素坐标
            fill_coords_list = []
            for i in range(filled_height.shape[0]):
                for j in range(filled_height.shape[1]):
                    if new_fill_mask[i, j] and filled_height[i, j] > 0.001:
                        # 转换为体素坐标
                        world_x = bbox_min[0] + i * grid.voxel_sizes[0]
                        world_y = bbox_min[1] + j * grid.voxel_sizes[0]
                        world_z = bbox_min[2] + filled_height[i, j]

                        voxel_coord = torch.tensor([
                            int((world_x - grid.origins[0]) / grid.voxel_sizes[0]),
                            int((world_y - grid.origins[0]) / grid.voxel_sizes[0]),
                            int((world_z - grid.origins[0]) / grid.voxel_sizes[0])
                        ], device=grid.device)

                        fill_coords_list.append(voxel_coord)

            # 添加新体素
            if fill_coords_list:
                fill_coords = torch.stack(fill_coords_list)
                print(f"通过形态学操作添加 {len(fill_coords)} 个体素")

                grid.enable_ijk(JaggedTensor([fill_coords]))

                # 为新体素设置适当的透明度
                new_alpha_values = torch.full(
                    (len(fill_coords),),
                    inv_sigmoid(0.25),  # 中等透明度
                    device=grid.device
                )

                # 扩展alpha参数
                combined_alpha = torch.cat([alpha.data.detach(), new_alpha_values])
                combined_alpha.requires_grad_(True)
                alpha.data = combined_alpha
                alpha.grad = None

    return grid, alpha

def visualize_grid_color(grid: GridBatch, rgb: JaggedTensor, ignore_disabled: bool = False):
    for b in range(grid.grid_count):
        grid_mask = grid.enabled_mask[b].jdata.cpu().numpy()
        if ignore_disabled:
            grid_mask.fill(True)

        grid_mesh = pcu.voxel_grid_geometry(
            grid.ijk[b].jdata.cpu().numpy()[grid_mask],
            0.1,
            grid.voxel_sizes[b].cpu().numpy())
        grid_color = rgb[b].jdata.cpu().numpy()[grid_mask].repeat(8, axis=0).reshape(-1, 3)

        ps.register_surface_mesh(
            f"grid_{b}", grid_mesh[0], grid_mesh[1], enabled=True
        ).add_color_quantity("color", grid_color, enabled=True)

def visualize_grid(grid: GridBatch):
  ps.init()

  feature = grid.grid_to_world(grid.ijk.float())
  z_values = feature.jdata[:, 2]

  z_min = z_values.min()
  z_max = z_values.max()
  z_values = (z_values - z_min) / (z_max - z_min)

  # 创建颜色数组
  colors = torch.zeros_like(feature.jdata)

  # 最低平面设置为白色 (z值小于0.42的部分)
  bottom_mask = z_values < 0.3
  above_mask = z_values >= 0.3
  grid.disable_ijk(grid.ijk.r_masked_select(bottom_mask))
  # 最低平面设为白色
  colors[bottom_mask] = 1.0  # RGB都设为1，呈现白色

  # 高于底部的部分处理
  above_indices = torch.where(above_mask)[0]
  above_z_values = z_values[above_indices]

  # 重新归一化到[0,1]
  normalized_z = (above_z_values - 0.3) / 0.7

  # 蓝色到绿色 (0 <= normalized_z <= 0.5)
  blue_to_green_indices = above_indices[normalized_z <= 0.5]
  blue_to_green_z = normalized_z[normalized_z <= 0.5]

  colors[blue_to_green_indices, 0] = 0  # 红色分量为0
  colors[blue_to_green_indices, 1] = blue_to_green_z * 2  # 绿色分量从0增加到1
  colors[blue_to_green_indices, 2] = 1 - blue_to_green_z * 2  # 蓝色分量从1减少到0

  # 绿色到红色 (0.5 < normalized_z <= 1.0)
  green_to_red_indices = above_indices[normalized_z > 0.5]
  green_to_red_z = normalized_z[normalized_z > 0.5]

  colors[green_to_red_indices, 0] = (green_to_red_z - 0.5) * 2  # 红色分量从0增加到1
  colors[green_to_red_indices, 1] = 1 - (green_to_red_z - 0.5) * 2  # 绿色分量从1减少到0
  colors[green_to_red_indices, 2] = 0  # 蓝色分量为0

  # 将颜色赋值给feature
  feature.jdata = colors

  # Visualization
  ps.remove_all_structures()
  visualize_grid_color(grid, feature)
  ps.set_ground_plane_mode("none")
  ps.show()

def render_opacity(grid: fvdb.GridBatch, feature: torch.Tensor, ray_orig=None, ray_dir=None):
    #确保光线起点和方向参数已提供
    assert ray_orig is not None and ray_dir is not None
    if torch.isnan(feature).any():
      print("feature contains NaN")#检查 feature 张量中是否存在 NaN（非数值）
      print("feature:", feature)

    pack_info, voxel_inds, out_times = grid.voxels_along_rays(ray_orig, ray_dir, 128, 0.0)
    pack_info = pack_info.jdata
    out_times = out_times.jdata
    voxel_inds = grid.ijk_to_index(voxel_inds).jdata

    _, _, opacity, _, _ = fvdb.utils.volume_render(
        sigmas=-torch.log(1 - feature[voxel_inds]),
        rgbs=torch.ones((voxel_inds.shape[0], 1), device=grid.device),
        deltaTs=torch.ones(voxel_inds.shape[0], device=grid.device),
        ts=out_times.mean(1),
        packInfo=pack_info, transmittanceThresh=0.0
    )
    return opacity

if __name__ == "__main__":
    print("=== 带空洞填充功能的体素渲染系统 ===")

    # 导入填充配置
    try:
        from fill_config import get_current_config, print_config, validate_config
        FILL_CONFIG = get_current_config()
        print_config(FILL_CONFIG)
        validate_config(FILL_CONFIG)
    except ImportError:
        print("警告: 无法导入fill_config.py，使用默认配置")
        # 默认配置
        FILL_CONFIG = {
            'SURFACE_FILL': {
                'enabled': True,
                'depth': 2,
                'threshold': 0.3,
                'interval': 10,
                'start_iteration': 10,
            },
            'NEIGHBOR_FILL': {
                'enabled': False,
                'iterations': 1,
                'threshold': 0.2,
                'interval': 15,
                'start_iteration': 15,
            },
            'VERTICAL_FILL': {
                'enabled': True,
                'fill_below': True,
                'fill_above': False,
                'max_gap': 2,
                'threshold': 0.25,
                'interval': 25,
                'start_iteration': 25,
            },
            'MORPHOLOGICAL_FILL': {
                'enabled': True,
                'kernel_size': 3,
                'iterations': 1,
                'interval': 20,
                'start_iteration': 20,
            },
            'FINAL_FILL': {
                'surface_depth': 3,
                'surface_threshold': 0.2,
                'morphological_kernel_size': 5,
                'morphological_iterations': 2,
            }
        }

    # 读取声纳回波数据
    keypings = lk.load_keypings(params.kp_dir, params.parse_ratio)
    if params.is_multi:
        keypings2 = lk.load_keypings(params.kp2_dir, params.parse_ratio)
        keypings.extend(keypings2)
    print("load keypings complated")

    # 初始化点云和场景信息
    init_PC, scene_info = spc.createPC(keypings, params.print_origPC, params.origPC_dir)
    print("init point cloud complated")

    # 进行射线近似
    render_masks = []
    ray_orig, ray_dir = [], []
    ray_opacity = []
    #keyping id ; keyping
    for kid, kp in enumerate(keypings):
        render_masks.append(kp.mask)
        ro, rd = kp.get_rays()
        ray_orig.append(ro)
        ray_dir.append(rd)
        ray_opacity.append(render_masks[kid].flatten())

    ray_orig = np.concatenate(ray_orig, axis=0)
    ray_orig = (ray_orig - scene_info.center) / scene_info.scale # 射线起点归一化
    ray_dir = np.concatenate(ray_dir, axis=0)
    ray_opacity = np.concatenate(ray_opacity, axis=0)

    # 检测每一行是否包含 NaN 值
    rows_without_nan = ~np.isnan(ray_orig).any(axis=1)

    # 使用布尔索引过滤掉包含 NaN 的行
    ray_orig = ray_orig[rows_without_nan]
    ray_dir = ray_dir[rows_without_nan]
    ray_opacity = ray_opacity[rows_without_nan]

    ray_orig = torch.from_numpy(ray_orig).to("cuda").float()
    ray_dir = torch.from_numpy(ray_dir).to("cuda").float()
    ray_opacity = torch.from_numpy(ray_opacity).to("cuda").float()

    grid = fvdb.sparse_grid_from_points(
        init_PC,
        voxel_sizes=1.0 / (params.render_res - 1),
        origins=[0.0] * 3,
        mutable=True
    )
    grid = grid.to("cuda")

    z_board = 5
    bbox_min = (grid.bbox[:, 0] - torch.tensor([5, 5, z_board]).long().to("cuda")).squeeze()
    bbox_max = (grid.bbox[:, 1] + torch.tensor([5, 5, z_board]).long().to("cuda")).squeeze()

    height_image0 = g2i.grid_to_height(grid, bbox_min, bbox_max, mod="mean") # 生成地底图

    # visualize_grid(grid) # 打印初始化网格
    grid = grid.coarsened_grid(4)
    # # visualize_grid(grid) # 打印降采样网格
    grid = grid.subdivided_grid(4)
    # visualize_grid(grid) # 打印升采样网格

    print(f"初始网格体素数量: {grid.total_voxels}")

    #---------------------------------------------------

    alpha = torch.full((grid.total_voxels, ), inv_sigmoid(params.init_alpha), device=grid.device, requires_grad=True)

    if torch.isnan(alpha).any():
        print("alpha contains NaN")
        print("alpha:", alpha)

    optimizer = torch.optim.Adam([alpha], lr=1.0)

    # 记录初始体素数量
    initial_voxel_count = grid.total_voxels
    merge_images = []
    writer = SummaryWriter(log_dir=params.log_dir)  # 日志保存路径

    # 在训练循环前记录开始时间
    print(f"当前显存占用: {torch.cuda.memory_allocated() / 1024**2:.2f} MB")
    total_start = time.time()

    print("\n=== 开始训练循环（包含空洞填充） ===")
    for it in range(100):

        sample_num = params.sample_num
        if params.random_sample:
            # 随机索引
            sub_inds = torch.randint(0, ray_orig.shape[0], (sample_num, ), device=grid.device)
        else:
            # 按顺序索引
            sub_inds = torch.arange(it*sample_num, (it+1)*sample_num) % ray_orig.shape[0]
            sub_inds = sub_inds.to(grid.device)

        pd_opacity = render_opacity(grid, torch.sigmoid(alpha), ray_orig=ray_orig[sub_inds], ray_dir=ray_dir[sub_inds])
        gt_opacity = ray_opacity[sub_inds]
        # 检查 pd_opacity 是否包含 NaN
        pd_opacity = torch.nan_to_num(pd_opacity, nan=0.0)
        loss = torch.mean(torch.abs(pd_opacity - gt_opacity))
        print("Iter", it, "Loss:", loss.item(), f"Voxels: {grid.total_voxels}")
        writer.add_scalar('Loss/train', loss.item(), it)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        alpha.data = torch.where(torch.isnan(alpha), torch.zeros_like(alpha), alpha)

        if it > 0 and it % 5 == 0:
            torch.cuda.empty_cache()
            with torch.no_grad():
                # 记录填充前的体素数量
                voxels_before_fill = grid.total_voxels

                # disable
                bad_mask = torch.sigmoid(alpha) < 0.1
                grid.disable_ijk(grid.ijk.r_masked_select(bad_mask))

                # random revive
                if it < 20:
                    enable_mask = torch.rand(grid.total_voxels, device=grid.device) < 0.01
                    grid.enable_ijk(grid.ijk.r_masked_select(enable_mask))

                # === 空洞填充逻辑 ===
                fill_performed = False

                # 表面向下填充
                surface_cfg = FILL_CONFIG['SURFACE_FILL']
                if (surface_cfg['enabled'] and it % surface_cfg['interval'] == 0 and it > surface_cfg['start_iteration']):
                    print(f"执行表面向下填充 (迭代 {it})")
                    grid, alpha = fill_surface_downward(
                        grid, alpha,
                        fill_depth=surface_cfg['depth'],
                        min_alpha_threshold=surface_cfg['threshold']
                    )
                    fill_performed = True

                # 3D邻域填充
                neighbor_cfg = FILL_CONFIG['NEIGHBOR_FILL']
                if (neighbor_cfg['enabled'] and it % neighbor_cfg['interval'] == 0 and it > neighbor_cfg['start_iteration']):
                    print(f"执行3D邻域填充 (迭代 {it})")
                    grid, alpha = fill_neighbors_3d(
                        grid, alpha,
                        iterations=neighbor_cfg['iterations'],
                        min_alpha_threshold=neighbor_cfg['threshold']
                    )
                    fill_performed = True

                # 垂直列填充
                vertical_cfg = FILL_CONFIG['VERTICAL_FILL']
                if (vertical_cfg['enabled'] and it % vertical_cfg['interval'] == 0 and it > vertical_cfg['start_iteration']):
                    print(f"执行垂直列填充 (迭代 {it})")
                    grid, alpha = fill_vertical_columns(
                        grid, alpha,
                        fill_below=vertical_cfg['fill_below'],
                        fill_above=vertical_cfg['fill_above'],
                        max_gap=vertical_cfg['max_gap'],
                        min_alpha_threshold=vertical_cfg['threshold']
                    )
                    fill_performed = True

                # 形态学空洞填充
                morph_cfg = FILL_CONFIG['MORPHOLOGICAL_FILL']
                if (morph_cfg['enabled'] and it % morph_cfg['interval'] == 0 and it > morph_cfg['start_iteration']):
                    print(f"执行形态学空洞填充 (迭代 {it})")
                    grid, alpha = fill_holes_morphological(
                        grid, alpha, bbox_min, bbox_max,
                        kernel_size=morph_cfg['kernel_size'],
                        iterations=morph_cfg['iterations']
                    )
                    fill_performed = True

                # 如果执行了填充操作且体素数量发生变化，重新创建优化器
                if fill_performed and grid.total_voxels != voxels_before_fill:
                    print(f"体素数量变化: {voxels_before_fill} -> {grid.total_voxels}")
                    print("重新创建优化器...")

                    # 确保alpha参数需要梯度
                    alpha.requires_grad_(True)

                    # 重新创建优化器
                    optimizer = torch.optim.Adam([alpha], lr=1.0)

                height_image1 = g2i.grid_to_height(grid, bbox_min, bbox_max)
                # if it % 10 == 0:
                #   g2i.show_images(height_image1)
                #   visualize_grid(grid)
                merge_image = g2i.merge_height(height_image0, height_image1)
                merge_images.append(merge_image)

    writer.close()

    # 训练结束后进行最终的空洞填充
    print("\n=== 训练完成，执行最终空洞填充 ===")
    with torch.no_grad():
        final_cfg = FILL_CONFIG['FINAL_FILL']

        # 最终表面向下填充
        if FILL_CONFIG['SURFACE_FILL']['enabled']:
            print("执行最终表面向下填充...")
            grid, alpha = fill_surface_downward(
                grid, alpha,
                fill_depth=final_cfg['surface_depth'],
                min_alpha_threshold=final_cfg['surface_threshold']
            )

        # 最终形态学填充
        if FILL_CONFIG['MORPHOLOGICAL_FILL']['enabled']:
            print("执行最终形态学填充...")
            grid, alpha = fill_holes_morphological(
                grid, alpha, bbox_min, bbox_max,
                kernel_size=final_cfg['morphological_kernel_size'],
                iterations=final_cfg['morphological_iterations']
            )

    print(f"最终网格体素数量: {grid.total_voxels}")

    visualize_grid(grid)
    final_image = merge_images[-1]
    # 初始化高度图
    g2i.show_images(height_image0)
    # 最终优化的高度图
    g2i.show_images(height_image1)
    # 合并高度图
    g2i.show_images(final_image)

    # 应用高斯卷积 - 动态平滑模块
    use_smooth = params.use_smooth if hasattr(params, 'use_smooth') else True

    if use_smooth:
        final_image = cg.confidence_gaussian(final_image, kernel_size = 11, sigma = 0.75, iter = 3)
        print("Applied dynamic smoothing")

    g2i.show_images(final_image)

    # 消融实验：计算图像质量指标
    def calculate_quality_metrics(image_tensor):
        """计算图像质量指标用于消融实验"""
        if len(image_tensor.shape) > 2:
            image = image_tensor[0].cpu().numpy()  # 取高度通道
        else:
            image = image_tensor.cpu().numpy()

        # 梯度幅值
        grad_x = torch.diff(image_tensor[0], dim=1, prepend=image_tensor[0][:, :1])
        grad_y = torch.diff(image_tensor[0], dim=0, prepend=image_tensor[0][:1, :])
        gradient_magnitude = torch.sqrt(grad_x**2 + grad_y**2)
        avg_gradient = torch.mean(gradient_magnitude).item()

        # 总变分
        tv_x = torch.mean(torch.abs(torch.diff(image_tensor[0], dim=1))).item()
        tv_y = torch.mean(torch.abs(torch.diff(image_tensor[0], dim=0))).item()
        total_variation = tv_x + tv_y

        # 表面粗糙度（拉普拉斯）
        laplacian = torch.zeros_like(image_tensor[0])
        if image_tensor[0].shape[0] > 2 and image_tensor[0].shape[1] > 2:
            laplacian[1:-1, 1:-1] = (image_tensor[0][:-2, 1:-1] + image_tensor[0][2:, 1:-1] +
                                    image_tensor[0][1:-1, :-2] + image_tensor[0][1:-1, 2:] -
                                    4 * image_tensor[0][1:-1, 1:-1])
        surface_roughness = torch.std(laplacian).item()

        print(f"\n=== 图像质量指标 (use_smooth={use_smooth}) ===")
        print(f"平均梯度幅值: {avg_gradient:.6f}")
        print(f"总变分: {total_variation:.6f}")
        print(f"表面粗糙度: {surface_roughness:.6f}")
        print(f"平均高度: {torch.mean(image_tensor[0]).item():.6f}")
        print(f"高度标准差: {torch.std(image_tensor[0]).item():.6f}")
        print("="*50)

        return {
            'gradient_magnitude': avg_gradient,
            'total_variation': total_variation,
            'surface_roughness': surface_roughness,
            'use_smooth': use_smooth
        }

    # 计算最终图像的质量指标
    quality_metrics = calculate_quality_metrics(final_image)

    z_scale = (bbox_max-bbox_min)[2] - z_board * 2
    # 打印最终的稠密点云
    pc = spc.image_to_pc(final_image[0], grid.voxel_sizes, z_scale)
    if params.print_finalPC:
        spc.print_pointcloud(pc, params.final_pc_dir)

    grid = fvdb.sparse_grid_from_points(
        pc,
        voxel_sizes=1.0 / (params.render_res - 1),
        origins=[0.0] * 3,
        mutable=True
    )

    visualize_grid(grid)

    print("\n=== 空洞填充渲染完成 ===")
    print("填充统计:")
    print(f"  最终体素数量: {grid.total_voxels}")
    print("  使用的填充方法:")
    for method, config_key in [
        ("表面向下填充", 'SURFACE_FILL'),
        ("3D邻域填充", 'NEIGHBOR_FILL'),
        ("垂直列填充", 'VERTICAL_FILL'),
        ("形态学填充", 'MORPHOLOGICAL_FILL')
    ]:
        if FILL_CONFIG[config_key]['enabled']:
            print(f"    ✓ {method}")
        else:
            print(f"    ✗ {method}")
    print("="*50)
