import torch
import matplotlib.pyplot as plt
import torch.nn.functional as F
import torchvision.transforms.functional as TF
import kornia

def extrapolate_boundary(image, max_distance=5):
    """
    使用距离加权外推填充边界缓冲区
    Args:
        image: [2, H, W] 张量，第0通道是高度，第1通道是置信度
        max_distance: 最大外推距离
    Returns:
        处理后的图像
    """
    device = image.device
    height, width = image.shape[-2:]
    
    # 找到有效数据区域
    valid_mask = image[0] > 0.001
    
    if not valid_mask.any():
        return image
    
    # 创建坐标网格
    y_coords, x_coords = torch.meshgrid(
        torch.arange(height, device=device),
        torch.arange(width, device=device),
        indexing='ij'
    )
    
    # 找到所有有效像素的坐标和值
    valid_indices = torch.where(valid_mask)
    valid_y = valid_indices[0]
    valid_x = valid_indices[1]
    valid_values = image[0][valid_mask]
    
    # 对每个无效像素，计算到所有有效像素的距离
    invalid_mask = ~valid_mask
    invalid_indices = torch.where(invalid_mask)
    
    if len(invalid_indices[0]) == 0:
        return image
    
    invalid_y = invalid_indices[0]
    invalid_x = invalid_indices[1]
    
    # 计算距离矩阵
    for i in range(len(invalid_y)):
        y_pos = invalid_y[i]
        x_pos = invalid_x[i]
        
        # 计算到所有有效像素的距离
        distances = torch.sqrt((valid_y.float() - y_pos) ** 2 + (valid_x.float() - x_pos) ** 2)
        
        # 只考虑距离在max_distance内的点
        close_mask = distances <= max_distance
        
        if close_mask.any():
            close_distances = distances[close_mask]
            close_values = valid_values[close_mask]
            
            # 距离加权插值 (距离越近权重越大)
            weights = 1.0 / (close_distances + 1e-6)
            weights = weights / weights.sum()
            
            interpolated_value = torch.sum(weights * close_values)
            confidence = 0.1 * (1.0 - close_distances.min() / max_distance)
            
            image[0, y_pos, x_pos] = interpolated_value
            image[1, y_pos, x_pos] = confidence
    
    return image

def smooth_boundary_transition(image, kernel_size=5):
    """
    平滑边界过渡，减少插值artifacts
    Args:
        image: [2, H, W] 张量
        kernel_size: 平滑核大小
    Returns:
        处理后的图像
    """
    device = image.device
    
    # 识别边界区域
    valid_mask = image[0] > 0.001
    kernel = torch.ones(kernel_size, kernel_size, device=device) / (kernel_size * kernel_size)
    
    # 膨胀操作找到边界附近区域
    dilated_mask = F.conv2d(
        valid_mask.float().unsqueeze(0).unsqueeze(0),
        kernel.unsqueeze(0).unsqueeze(0),
        padding=kernel_size//2
    ) > 0.01
    
    # 边界区域 = 膨胀区域 - 原始有效区域
    boundary_mask = dilated_mask.squeeze() & ~valid_mask
    
    if boundary_mask.any():
        # 对高度图进行轻度高斯平滑
        smoothed_height = kornia.filters.gaussian_blur2d(
            image[0:1].unsqueeze(0), 
            (3, 3), 
            (1.0, 1.0)
        ).squeeze()
        
        # 在边界区域应用平滑
        image[0][boundary_mask] = smoothed_height[boundary_mask]
        
        # 调整边界区域的置信度
        image[1][boundary_mask] = torch.clamp(image[1][boundary_mask] * 0.8, 0.01, 0.5)
    
    return image

def advanced_boundary_fill(image, method="weighted"):
    """
    高级边界填充方法
    Args:
        image: [2, H, W] 张量
        method: 'weighted', 'inpaint', 'nearest'
    Returns:
        处理后的图像
    """
    device = image.device
    height, width = image.shape[-2:]
    
    if method == "nearest":
        # 最近邻填充
        valid_mask = image[0] > 0.001
        if not valid_mask.any():
            return image
            
        # 计算到最近有效像素的距离
        from scipy.ndimage import distance_transform_edt
        
        # 转为numpy计算距离变换
        mask_np = valid_mask.cpu().numpy()
        dist_transform = distance_transform_edt(~mask_np)
        
        # 找最近邻索引
        from scipy.ndimage import distance_transform_edt
        indices = distance_transform_edt(~mask_np, return_indices=True)[1]
        
        # 使用最近邻值填充
        invalid_mask = ~valid_mask
        if invalid_mask.any():
            y_nearest = torch.from_numpy(indices[0]).to(device)
            x_nearest = torch.from_numpy(indices[1]).to(device)
            
            image[0][invalid_mask] = image[0][y_nearest[invalid_mask], x_nearest[invalid_mask]]
            
            # 距离衰减的置信度
            dist_torch = torch.from_numpy(dist_transform).to(device)
            confidence = 0.1 * torch.exp(-dist_torch[invalid_mask] / 5.0)
            image[1][invalid_mask] = confidence
    
    elif method == "inpaint":
        # 基于扩散的修复
        valid_mask = image[0] > 0.001
        if valid_mask.sum() < height * width * 0.1:  # 有效数据太少
            return image
            
        # 简化的泊松修复近似
        invalid_mask = ~valid_mask
        if invalid_mask.any():
            # 迭代更新无效像素
            temp_image = image[0].clone()
            for _ in range(10):  # 10次迭代
                # 拉普拉斯算子
                laplacian = F.conv2d(
                    temp_image.unsqueeze(0).unsqueeze(0),
                    torch.tensor([[[[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]]]]).float().to(device),
                    padding=1
                ).squeeze()
                
                # 只更新无效区域
                temp_image[invalid_mask] = temp_image[invalid_mask] - 0.1 * laplacian[invalid_mask]
            
            image[0][invalid_mask] = temp_image[invalid_mask]
            image[1][invalid_mask] = 0.05  # 低置信度
    
    return image

def show_images(image):
  image_np = image.to("cpu").numpy()
  plt.figure(figsize=(20, 5))  # 设置画布大小

  # 第一张图像
  plt.subplot(1, 2, 1)  # 1行2列，第1个位置
  plt.imshow(image_np[0], cmap='gray')
  plt.colorbar()
  plt.title("Elevation Map")

  # 第二张图像
  plt.subplot(1, 2, 2)  # 1行2列，第2个位置
  plt.imshow(image_np[1], cmap='gray')
  plt.colorbar()
  plt.title("Confidence Map")

  plt.show()

def grid_to_height(grid, bbox_min, bbox_max, mod = "max", show = False):
  bbox_size = bbox_max - bbox_min

  grid_mask = grid.enabled_mask[0].jdata
  ijk_data = grid.ijk.jdata[grid_mask] # 取出体素坐标
  #------------------------------------------------
  height = bbox_size[1] + 1# 定义图像高度
  width = bbox_size[0] + 1# 定义图像宽度
  image = torch.zeros(2, height, width, dtype=torch.float32).to("cuda") # 创建图像张量


  x_coords = ijk_data[:, 0] - bbox_min[0] # 提取 x 坐标，并把最小值设为0
  y_coords = ijk_data[:, 1] - bbox_min[1] # 提取 y 坐标，并把最小值设为0

  z_values = ((ijk_data[:, 2] - bbox_min[2]).float() / bbox_size[2]).to("cuda") # 提取 z 值，并归一化

  # 将 x, y 坐标转换为索引
  indices = (y_coords * width + x_coords).to("cuda").long()
  
  # 使用 scatter_ 将 z 值按 (x, y) 坐标放入 image 中，取最大值
  if (mod == "max"): # 优化结果
    image[1].fill_(0.01) # 没有被优化的像素置信度取0.1 1置信度，0深度
    image[0].view(-1).scatter_reduce_(
          dim=0,  # 沿一维索引操作
          index=indices,  # 目标索引
          src=z_values,  # 要填充的值
          reduce="amax",  # 对重复位置取最大值
          include_self=False  # 不包括 tensor 的初始值
      )
    image[1].view(-1)[indices] = 0.99 # 重建的像素置信度为0.99
  elif (mod == "mean"):
    # 填充第二个通道为常量 0.3
    image[1].fill_(0.01) # 没有被深度初始化的位置置信度为0.1
    image[0].view(-1).scatter_reduce_(
          dim=0,  # 沿一维索引操作
          index=indices,  # 目标索引
          src=z_values,  # 要填充的值
          reduce="mean",  # 对重复位置取均值
          include_self=False  # 不包括 tensor 的初始值
      )
    image[1].view(-1)[indices] = 0.3 # 设置第二个通道的值为 0.3
    height_image = image[0].reshape(1, 1, height, width)
    median_image = kornia.filters.median_blur(height_image, kernel_size=(11, 11))
    gaussian_image = kornia.filters.gaussian_blur2d(median_image, (15, 15), (9, 9))
    image[0] = gaussian_image.reshape(height, width)
    mask = image[0] > 0.001  # 不是最深的地方都设为0.1
    image[1][mask] = 0.1

  # 边界处理优化
  # if bbox_size.max() > 10:  # 只在有足够缓冲区时处理
  #   # 应用边界外推
  #   image = extrapolate_boundary(image, max_distance=5)
  #   # 平滑边界过渡
  #   image = smooth_boundary_transition(image, kernel_size=5)

  if (show):
    show_images(image)

  return image

def merge_height(height_image0, height_image1, show = False):
  merge_image = torch.zeros_like(height_image0)
  # 比较置信度并选择更大的值
  mask = height_image0[1] > height_image1[1]  # 比较置信度
  merge_image[0] = torch.where(mask, height_image0[0], height_image1[0])  # 选择像素值
  merge_image[1] = torch.where(mask, height_image0[1], height_image1[1])  # 选择置信度

  if (show):
    show_images(merge_image)
  
  return merge_image