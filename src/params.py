import numpy as np
#------------------------输入参数------------------------#
#! 读取声纳关键帧数据
kp_dir = "/home/<USER>/code/water_ws/output/zhanghe2024/sim1kingpings1.dat"
# kp_dir = "/home/<USER>/code/water_ws/output/kingpings.dat"
is_multi =  True
kp2_dir = "/home/<USER>/code/water_ws/output/zhanghe2024/sim1kingpings2.dat"

ping_res = 1000 #! 输入数据的ping分辨率 

range_max = 100 #! 声纳的最大量程

parse_ratio = 1 # 读取多少比例的数据 0-1

tbl = np.array([-1.3035, -0.18433, 0.801486])
Rbl= np.array([[1.0, 0.0, 0.0],
      [0.0, 0.39874906892524631, 0.91706007438512405],
      [0.0, -0.91706007438512405, 0.39874906892524631]])

# right-sonar
tbr = np.array([-1.3035, 0.18433, 0.801486])
Rbr = np.array([[1.0, 0.0, 0.0],
      [0.0, 0.39874906892524631, -0.91706007438512405],
      [0.0, 0.91706007438512405, 0.39874906892524631]])
#-------------------------处理参数-------------------------#
intensity_th = 80.0 #! 强度阈值

render_res = 1000 # 创建VDB的分辨率

init_alpha = 0.09 # 初始的不透明度

random_sample = True # 是否随机采样

iter_num = 100 # 迭代次数

sample_num = 5000000 # 随机采样的点数
#--------------------------输出参数---------------------------#
# 原始点云保存路径
print_origPC = False
origPC_dir = "/home/<USER>/code/water_ws/output/zhanghe2024/orig_pc.txt"
# 重建后点云保存路径
print_finalPC = True
final_pc_dir = "/home/<USER>/code/water_ws/output/zhanghe2024/final_pc.txt"
# tensorboard日志保存路径
log_dir = "/home/<USER>/code/water_ws/logs"

# 消融实验控制参数
use_smooth = True

# 空洞填充控制参数
use_surface_fill = True  # 是否启用表面向下填充
surface_fill_depth = 2  # 表面向下填充深度
surface_fill_threshold = 0.3  # 表面体素的最小透明度阈值
surface_fill_interval = 10  # 表面填充的迭代间隔

use_morphological_fill = True  # 是否启用形态学填充
morphological_kernel_size = 3  # 形态学操作核大小
morphological_iterations = 1  # 形态学操作迭代次数
morphological_fill_interval = 20  # 形态学填充的迭代间隔

# 最终填充参数
final_surface_fill_depth = 3  # 最终表面填充深度
final_surface_fill_threshold = 0.2  # 最终表面填充阈值
final_morphological_kernel_size = 5  # 最终形态学核大小
final_morphological_iterations = 2  # 最终形态学迭代次数

# 简化填充方法参数（不依赖外部库）
use_neighbor_fill = False  # 是否启用3D邻域填充
neighbor_fill_iterations = 1  # 邻域填充迭代次数
neighbor_fill_interval = 15  # 邻域填充的迭代间隔

use_vertical_fill = True  # 是否启用垂直列填充
vertical_fill_below = True  # 是否向下填充
vertical_fill_above = False  # 是否向上填充
vertical_max_gap = 2  # 垂直填充最大间隙
vertical_fill_interval = 25  # 垂直填充的迭代间隔
