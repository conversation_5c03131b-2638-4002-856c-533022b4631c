import numpy as np
#------------------------输入参数------------------------#
#! 读取声纳关键帧数据
kp_dir = "/home/<USER>/code/water_ws/output/zhanghe2024/sim1kingpings1.dat"
# kp_dir = "/home/<USER>/code/water_ws/output/kingpings.dat"
is_multi =  True
kp2_dir = "/home/<USER>/code/water_ws/output/zhanghe2024/sim1kingpings2.dat"

ping_res = 1000 #! 输入数据的ping分辨率 

range_max = 100 #! 声纳的最大量程

parse_ratio = 1 # 读取多少比例的数据 0-1

tbl = np.array([-1.3035, -0.18433, 0.801486])
Rbl= np.array([[1.0, 0.0, 0.0],
      [0.0, 0.39874906892524631, 0.91706007438512405],
      [0.0, -0.91706007438512405, 0.39874906892524631]])

# right-sonar
tbr = np.array([-1.3035, 0.18433, 0.801486])
Rbr = np.array([[1.0, 0.0, 0.0],
      [0.0, 0.39874906892524631, -0.91706007438512405],
      [0.0, 0.91706007438512405, 0.39874906892524631]])
#-------------------------处理参数-------------------------#
intensity_th = 80.0 #! 强度阈值

render_res = 1000 # 创建VDB的分辨率

init_alpha = 0.09 # 初始的不透明度

random_sample = True # 是否随机采样

iter_num = 100 # 迭代次数

sample_num = 5000000 # 随机采样的点数
#--------------------------输出参数---------------------------#
# 原始点云保存路径
print_origPC = False
origPC_dir = "/home/<USER>/code/water_ws/output/zhanghe2024/orig_pc.txt"
# 重建后点云保存路径
print_finalPC = True
final_pc_dir = "/home/<USER>/code/water_ws/output/zhanghe2024/final_pc.txt"
# tensorboard日志保存路径
log_dir = "/home/<USER>/code/water_ws/logs"
fill_depth = 8
# 消融实验控制参数
use_smooth = True
