#!/usr/bin/env python3
"""
SSS SLAM重建质量评估使用示例
演示如何评估render.py生成的final_image和其他重建结果
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from reconstruction_metrics import ReconstructionMetrics
import torch
import numpy as np
from PIL import Image

def evaluate_current_results():
    """评估当前项目中的重建结果"""
    
    # 初始化评估器
    metrics = ReconstructionMetrics()
    print("初始化评估器完成")
    
    # 定义文件路径 - 根据你的实际文件调整
    base_dir = "/home/<USER>/code/water_ws/src/SSS_SLAM-uuv"
    
    # 预测结果路径
    pred_paths = {
        "height": f"{base_dir}/scripts/output_1024_height.png",
        "binary": f"{base_dir}/scripts/output_1024_binary.png", 
        "pcd": f"{base_dir}/render/merge_map.pcd",
        "mesh": f"{base_dir}/render/merge_map.ply"
    }
    
    # Ground Truth路径 (如果有的话，需要你提供)
    gt_paths = {
        "height": None,  # 设置为实际GT路径
        "binary": None,  # 设置为实际GT路径
        "pcd": None,     # 设置为实际GT路径
        "mesh": None     # 设置为实际GT路径
    }
    
    print("检查预测结果文件:")
    for name, path in pred_paths.items():
        exists = os.path.exists(path) if path else False
        print(f"  {name}: {path} - {'存在' if exists else '不存在'}")
    
    # 如果你有ground truth数据，设置路径并评估
    if any(gt_paths.values()):
        print("\n执行完整评估...")
        results = metrics.evaluate_reconstruction(
            pred_height_path=pred_paths["height"],
            pred_binary_path=pred_paths["binary"],
            pred_pcd_path=pred_paths["pcd"], 
            pred_mesh_path=pred_paths["mesh"],
            gt_height_path=gt_paths["height"],
            gt_binary_path=gt_paths["binary"],
            gt_pcd_path=gt_paths["pcd"],
            gt_mesh_path=gt_paths["mesh"]
        )
        metrics.print_metrics_summary(results)
    else:
        print("\n未提供Ground Truth，执行单项评估示例...")
        
        # 示例：仅评估现有结果的统计信息
        if os.path.exists(pred_paths["pcd"]):
            try:
                import open3d as o3d
                pcd = o3d.io.read_point_cloud(pred_paths["pcd"])
                points = np.asarray(pcd.points)
                print(f"\n点云统计信息:")
                print(f"  点数: {len(points)}")
                print(f"  X范围: [{points[:,0].min():.2f}, {points[:,0].max():.2f}]")
                print(f"  Y范围: [{points[:,1].min():.2f}, {points[:,1].max():.2f}]") 
                print(f"  Z范围: [{points[:,2].min():.2f}, {points[:,2].max():.2f}]")
            except Exception as e:
                print(f"点云分析失败: {e}")

def evaluate_from_final_image(final_image, output_dir="./evaluation_output"):
    """
    从render.py的final_image直接评估
    
    Args:
        final_image: render.py中的final_image张量 [2, H, W]
        output_dir: 输出目录
    """
    
    print("从final_image评估重建质量...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取高度图和置信度图
    if len(final_image.shape) == 3 and final_image.shape[0] == 2:
        height_map = final_image[0].cpu().numpy()  # 深度通道
        confidence_map = final_image[1].cpu().numpy()  # 置信度通道
        
        # 保存为图像
        height_normalized = ((height_map - height_map.min()) / 
                           (height_map.max() - height_map.min()) * 255).astype(np.uint8)
        confidence_normalized = (confidence_map * 255).astype(np.uint8)
        
        height_path = os.path.join(output_dir, "final_height.png")
        confidence_path = os.path.join(output_dir, "final_confidence.png")
        
        Image.fromarray(height_normalized).save(height_path)
        Image.fromarray(confidence_normalized).save(confidence_path)
        
        print(f"已保存高度图: {height_path}")
        print(f"已保存置信度图: {confidence_path}")
        
        # 生成二值图
        binary_map = (confidence_map > 0.1).astype(np.uint8) * 255
        binary_path = os.path.join(output_dir, "final_binary.png")
        Image.fromarray(binary_map).save(binary_path)
        print(f"已保存二值图: {binary_path}")
        
        # 计算基础统计信息
        valid_pixels = np.sum(confidence_map > 0.01)
        total_pixels = confidence_map.size
        coverage = valid_pixels / total_pixels
        
        print(f"\nfinal_image统计信息:")
        print(f"  分辨率: {height_map.shape}")
        print(f"  高度范围: [{height_map.min():.3f}, {height_map.max():.3f}]")
        print(f"  有效像素: {valid_pixels} / {total_pixels}")
        print(f"  覆盖率: {coverage:.2%}")
        
        # 如果有GT，可以进行比较
        return {
            "height_path": height_path,
            "binary_path": binary_path,
            "confidence_path": confidence_path,
            "stats": {
                "coverage": coverage,
                "valid_pixels": valid_pixels,
                "height_range": [float(height_map.min()), float(height_map.max())]
            }
        }
    else:
        print(f"final_image格式不正确，期望[2, H, W]，实际: {final_image.shape}")
        return None

def compare_with_existing_results(final_image_path, existing_height_path):
    """
    比较final_image与现有结果
    
    Args:
        final_image_path: final_image生成的高度图路径
        existing_height_path: 现有的高度图路径
    """
    
    metrics = ReconstructionMetrics()
    
    print("比较final_image与现有结果...")
    
    if os.path.exists(final_image_path) and os.path.exists(existing_height_path):
        # 使用现有结果作为"参考"进行比较
        results = metrics.compute_height_map_rmse(
            final_image_path, 
            existing_height_path,
            ignore_zero=True
        )
        
        print("比较结果:")
        for key, value in results.items():
            print(f"  {key}: {value}")
    else:
        print("文件不存在，无法比较")

if __name__ == "__main__":
    print("SSS SLAM重建质量评估示例\n")
    
    # 评估现有结果
    evaluate_current_results()
    
    print("\n" + "="*60)
    print("如需从render.py的final_image评估，请使用:")
    print("evaluate_from_final_image(final_image)")
    print("="*60)