"""
空洞填充配置文件
可以通过修改这个文件来调整填充参数，而不需要修改主代码
"""

# =============================================================================
# 基础填充配置
# =============================================================================

# 表面向下填充配置
SURFACE_FILL = {
    'enabled': True,                    # 是否启用表面向下填充
    'depth': 2,                        # 向下填充的深度（体素单位）
    'threshold': 0.3,                  # 被认为是表面的最小透明度阈值
    'interval': 10,                    # 执行间隔（每N次迭代执行一次）
    'start_iteration': 10,             # 开始执行的迭代次数
}

# 3D邻域填充配置
NEIGHBOR_FILL = {
    'enabled': False,                   # 是否启用3D邻域填充
    'iterations': 1,                   # 邻域填充迭代次数
    'threshold': 0.2,                  # 新体素的透明度阈值
    'interval': 15,                    # 执行间隔
    'start_iteration': 15,             # 开始执行的迭代次数
}

# 垂直列填充配置
VERTICAL_FILL = {
    'enabled': True,                   # 是否启用垂直列填充
    'fill_below': True,                # 是否向下填充
    'fill_above': False,               # 是否向上填充
    'max_gap': 2,                      # 最大填充间隙
    'threshold': 0.25,                 # 新体素的透明度阈值
    'interval': 25,                    # 执行间隔
    'start_iteration': 25,             # 开始执行的迭代次数
}

# 形态学填充配置
MORPHOLOGICAL_FILL = {
    'enabled': True,                   # 是否启用形态学填充
    'kernel_size': 3,                  # 形态学操作核大小
    'iterations': 1,                   # 形态学操作迭代次数
    'interval': 20,                    # 执行间隔
    'start_iteration': 20,             # 开始执行的迭代次数
}

# =============================================================================
# 最终填充配置（训练结束后执行）
# =============================================================================

FINAL_FILL = {
    'surface_depth': 3,                # 最终表面填充深度
    'surface_threshold': 0.2,          # 最终表面填充阈值
    'morphological_kernel_size': 5,    # 最终形态学核大小
    'morphological_iterations': 2,     # 最终形态学迭代次数
}

# =============================================================================
# 预设配置
# =============================================================================

def get_conservative_config():
    """保守填充配置 - 轻微填充，保持原始特征"""
    config = {
        'SURFACE_FILL': {
            'enabled': True,
            'depth': 1,
            'threshold': 0.4,
            'interval': 20,
            'start_iteration': 15,
        },
        'NEIGHBOR_FILL': {
            'enabled': False,
            'iterations': 1,
            'threshold': 0.3,
            'interval': 30,
            'start_iteration': 30,
        },
        'VERTICAL_FILL': {
            'enabled': False,
            'fill_below': True,
            'fill_above': False,
            'max_gap': 1,
            'threshold': 0.3,
            'interval': 40,
            'start_iteration': 40,
        },
        'MORPHOLOGICAL_FILL': {
            'enabled': False,
            'kernel_size': 3,
            'iterations': 1,
            'interval': 50,
            'start_iteration': 50,
        },
        'FINAL_FILL': {
            'surface_depth': 2,
            'surface_threshold': 0.3,
            'morphological_kernel_size': 3,
            'morphological_iterations': 1,
        }
    }
    return config

def get_aggressive_config():
    """激进填充配置 - 强力填充，消除大部分空洞"""
    config = {
        'SURFACE_FILL': {
            'enabled': True,
            'depth': 4,
            'threshold': 0.2,
            'interval': 5,
            'start_iteration': 5,
        },
        'NEIGHBOR_FILL': {
            'enabled': True,
            'iterations': 2,
            'threshold': 0.15,
            'interval': 10,
            'start_iteration': 10,
        },
        'VERTICAL_FILL': {
            'enabled': True,
            'fill_below': True,
            'fill_above': True,
            'max_gap': 4,
            'threshold': 0.2,
            'interval': 15,
            'start_iteration': 15,
        },
        'MORPHOLOGICAL_FILL': {
            'enabled': True,
            'kernel_size': 5,
            'iterations': 2,
            'interval': 10,
            'start_iteration': 10,
        },
        'FINAL_FILL': {
            'surface_depth': 5,
            'surface_threshold': 0.15,
            'morphological_kernel_size': 7,
            'morphological_iterations': 3,
        }
    }
    return config

def get_balanced_config():
    """平衡填充配置 - 在效果和性能之间平衡"""
    config = {
        'SURFACE_FILL': SURFACE_FILL.copy(),
        'NEIGHBOR_FILL': NEIGHBOR_FILL.copy(),
        'VERTICAL_FILL': VERTICAL_FILL.copy(),
        'MORPHOLOGICAL_FILL': MORPHOLOGICAL_FILL.copy(),
        'FINAL_FILL': FINAL_FILL.copy(),
    }
    return config

def get_no_external_deps_config():
    """无外部依赖配置 - 不使用需要opencv的形态学填充"""
    config = {
        'SURFACE_FILL': {
            'enabled': True,
            'depth': 3,
            'threshold': 0.25,
            'interval': 8,
            'start_iteration': 8,
        },
        'NEIGHBOR_FILL': {
            'enabled': True,
            'iterations': 1,
            'threshold': 0.2,
            'interval': 15,
            'start_iteration': 15,
        },
        'VERTICAL_FILL': {
            'enabled': True,
            'fill_below': True,
            'fill_above': False,
            'max_gap': 3,
            'threshold': 0.25,
            'interval': 20,
            'start_iteration': 20,
        },
        'MORPHOLOGICAL_FILL': {
            'enabled': False,  # 禁用形态学填充
            'kernel_size': 3,
            'iterations': 1,
            'interval': 30,
            'start_iteration': 30,
        },
        'FINAL_FILL': {
            'surface_depth': 4,
            'surface_threshold': 0.2,
            'morphological_kernel_size': 3,
            'morphological_iterations': 1,
        }
    }
    return config

# =============================================================================
# 配置选择
# =============================================================================

# 选择要使用的配置（可以修改这里来切换不同的预设）
CURRENT_CONFIG = "balanced"  # 选项: "conservative", "aggressive", "balanced", "no_external_deps"

def get_current_config():
    """获取当前选择的配置"""
    if CURRENT_CONFIG == "conservative":
        return get_conservative_config()
    elif CURRENT_CONFIG == "aggressive":
        return get_aggressive_config()
    elif CURRENT_CONFIG == "balanced":
        return get_balanced_config()
    elif CURRENT_CONFIG == "no_external_deps":
        return get_no_external_deps_config()
    else:
        # 默认使用平衡配置
        return get_balanced_config()

# =============================================================================
# 配置验证和打印
# =============================================================================

def print_config(config=None):
    """打印当前配置"""
    if config is None:
        config = get_current_config()
    
    print("=== 空洞填充配置 ===")
    print(f"当前配置: {CURRENT_CONFIG}")
    print()
    
    for method_name, method_config in config.items():
        print(f"{method_name}:")
        for key, value in method_config.items():
            print(f"  {key}: {value}")
        print()

def validate_config(config=None):
    """验证配置的合理性"""
    if config is None:
        config = get_current_config()
    
    warnings = []
    
    # 检查表面填充
    if config['SURFACE_FILL']['enabled']:
        if config['SURFACE_FILL']['depth'] > 5:
            warnings.append("表面填充深度过大，可能导致过度填充")
        if config['SURFACE_FILL']['threshold'] < 0.1:
            warnings.append("表面填充阈值过低，可能填充过多体素")
    
    # 检查邻域填充
    if config['NEIGHBOR_FILL']['enabled']:
        if config['NEIGHBOR_FILL']['iterations'] > 3:
            warnings.append("邻域填充迭代次数过多，可能导致性能问题")
    
    # 检查垂直填充
    if config['VERTICAL_FILL']['enabled']:
        if config['VERTICAL_FILL']['max_gap'] > 5:
            warnings.append("垂直填充最大间隙过大，可能填充不相关区域")
    
    # 检查形态学填充
    if config['MORPHOLOGICAL_FILL']['enabled']:
        if config['MORPHOLOGICAL_FILL']['kernel_size'] > 7:
            warnings.append("形态学核大小过大，可能过度平滑")
    
    if warnings:
        print("配置警告:")
        for warning in warnings:
            print(f"  ⚠️  {warning}")
        print()
    else:
        print("✅ 配置验证通过")

if __name__ == "__main__":
    # 打印当前配置
    current_config = get_current_config()
    print_config(current_config)
    validate_config(current_config)
