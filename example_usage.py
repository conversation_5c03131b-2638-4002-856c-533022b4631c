#!/usr/bin/env python3
"""
空洞填充功能使用示例
演示如何在不同场景下使用各种填充方法
"""

import torch
import numpy as np
import src.params as params

def example_1_basic_surface_fill():
    """示例1: 基本的表面向下填充"""
    print("=== 示例1: 基本表面向下填充 ===")
    
    # 在params.py中设置以下参数:
    print("推荐参数设置:")
    print("use_surface_fill = True")
    print("surface_fill_depth = 2")
    print("surface_fill_threshold = 0.3")
    print("surface_fill_interval = 10")
    print()
    
    print("这种配置适用于:")
    print("- 表面相对平整的场景")
    print("- 需要填补表面下方小空洞")
    print("- 计算资源有限的情况")
    print()

def example_2_aggressive_fill():
    """示例2: 激进的空洞填充"""
    print("=== 示例2: 激进空洞填充 ===")
    
    print("推荐参数设置:")
    print("use_surface_fill = True")
    print("surface_fill_depth = 4")
    print("surface_fill_threshold = 0.2")
    print("surface_fill_interval = 5")
    print()
    print("use_morphological_fill = True")
    print("morphological_kernel_size = 5")
    print("morphological_iterations = 2")
    print("morphological_fill_interval = 10")
    print()
    print("use_vertical_fill = True")
    print("vertical_fill_below = True")
    print("vertical_max_gap = 3")
    print("vertical_fill_interval = 15")
    print()
    
    print("这种配置适用于:")
    print("- 空洞较多的稀疏数据")
    print("- 需要完整表面重建")
    print("- GPU内存充足的情况")
    print()

def example_3_conservative_fill():
    """示例3: 保守的空洞填充"""
    print("=== 示例3: 保守空洞填充 ===")
    
    print("推荐参数设置:")
    print("use_surface_fill = True")
    print("surface_fill_depth = 1")
    print("surface_fill_threshold = 0.4")
    print("surface_fill_interval = 20")
    print()
    print("use_morphological_fill = False")
    print("use_vertical_fill = False")
    print("use_neighbor_fill = False")
    print()
    
    print("这种配置适用于:")
    print("- 数据质量较好的场景")
    print("- 只需要轻微填充")
    print("- 保持原始数据特征")
    print()

def example_4_no_external_deps():
    """示例4: 不依赖外部库的填充"""
    print("=== 示例4: 无外部依赖填充 ===")
    
    print("推荐参数设置:")
    print("use_surface_fill = True")
    print("use_morphological_fill = False  # 需要opencv和scipy")
    print()
    print("use_neighbor_fill = True")
    print("neighbor_fill_iterations = 1")
    print("neighbor_fill_interval = 20")
    print()
    print("use_vertical_fill = True")
    print("vertical_fill_below = True")
    print("vertical_max_gap = 2")
    print()
    
    print("这种配置适用于:")
    print("- 无法安装opencv-python和scipy")
    print("- 只使用PyTorch和FVDB")
    print("- 简单的填充需求")
    print()

def check_current_config():
    """检查当前参数配置"""
    print("=== 当前参数配置 ===")
    
    fill_methods = []
    
    if hasattr(params, 'use_surface_fill') and params.use_surface_fill:
        fill_methods.append(f"表面填充 (深度: {getattr(params, 'surface_fill_depth', 2)})")
    
    if hasattr(params, 'use_morphological_fill') and params.use_morphological_fill:
        fill_methods.append(f"形态学填充 (核大小: {getattr(params, 'morphological_kernel_size', 3)})")
    
    if hasattr(params, 'use_neighbor_fill') and params.use_neighbor_fill:
        fill_methods.append(f"邻域填充 (迭代: {getattr(params, 'neighbor_fill_iterations', 1)})")
    
    if hasattr(params, 'use_vertical_fill') and params.use_vertical_fill:
        fill_methods.append(f"垂直填充 (间隙: {getattr(params, 'vertical_max_gap', 2)})")
    
    if fill_methods:
        print("启用的填充方法:")
        for method in fill_methods:
            print(f"  - {method}")
    else:
        print("未启用任何填充方法")
    
    print()

def performance_tips():
    """性能优化建议"""
    print("=== 性能优化建议 ===")
    
    print("1. 内存优化:")
    print("   - 减少填充深度和间隙大小")
    print("   - 增加填充执行间隔")
    print("   - 在训练过程中定期清理GPU缓存")
    print()
    
    print("2. 速度优化:")
    print("   - 优先使用表面填充（最快）")
    print("   - 谨慎使用形态学填充（较慢）")
    print("   - 邻域填充适中")
    print()
    
    print("3. 质量平衡:")
    print("   - 表面填充: 快速但可能不够完整")
    print("   - 形态学填充: 完整但计算开销大")
    print("   - 垂直填充: 适合层状结构")
    print("   - 邻域填充: 适合局部空洞")
    print()

def troubleshooting():
    """故障排除指南"""
    print("=== 故障排除指南 ===")
    
    print("问题1: CUDA内存不足")
    print("解决方案:")
    print("  - 减少 surface_fill_depth")
    print("  - 增加填充执行间隔")
    print("  - 在训练循环中添加 torch.cuda.empty_cache()")
    print()
    
    print("问题2: 填充效果不明显")
    print("解决方案:")
    print("  - 降低 surface_fill_threshold")
    print("  - 增加 surface_fill_depth")
    print("  - 启用多种填充方法组合")
    print()
    
    print("问题3: 过度填充")
    print("解决方案:")
    print("  - 提高透明度阈值")
    print("  - 减少填充深度")
    print("  - 增加执行间隔")
    print()
    
    print("问题4: 形态学填充失败")
    print("解决方案:")
    print("  - 安装依赖: pip install opencv-python scipy")
    print("  - 或者禁用形态学填充，使用其他方法")
    print()

def main():
    """主函数"""
    print("空洞填充功能使用指南")
    print("=" * 50)
    print()
    
    # 检查当前配置
    check_current_config()
    
    # 显示使用示例
    example_1_basic_surface_fill()
    example_2_aggressive_fill()
    example_3_conservative_fill()
    example_4_no_external_deps()
    
    # 性能建议
    performance_tips()
    
    # 故障排除
    troubleshooting()
    
    print("=" * 50)
    print("使用方法:")
    print("1. 根据您的需求选择合适的参数配置")
    print("2. 修改 src/params.py 中的相关参数")
    print("3. 运行 python render.py 开始训练")
    print("4. 观察填充效果并根据需要调整参数")
    print()
    print("测试填充功能:")
    print("python test_fill.py")

if __name__ == "__main__":
    main()
