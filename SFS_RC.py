import cv2
import numpy as np
import matplotlib.pyplot as plt
import open3d as o3d
import time
def sfs_reconstruct_and_visualize_3d(image_path, pixel_width_meters, pixel_height_meters, k_factor, output_mesh_path=None, show_plots=True):
    """
    使用简化的SFS从单条声纳图像重建3D网格，并进行可视化。

    参数:
    image_path (str): 输入的单条测线声纳图像路径。
    pixel_width_meters (float): 图像中每个像素在x方向代表的宽度（米）。
    pixel_height_meters (float): 图像中每个像素在y方向代表的高度（米）。
    k_factor (float): 强度到坡度的比例因子（关键调整参数）。
    output_mesh_path (str, optional): 输出3D网格的保存路径 (e.g., 'sfs_mesh_pass1.ply')。如果为None，则不保存。
    show_plots (bool): 是否显示2D图像的中间过程。
    """

    # --- 第1步：加载并预处理图像 ---
    print(f"1. 正在从 '{image_path}' 加载图像...")
    sonar_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if sonar_image is None:
        print(f"错误：无法加载图像 '{image_path}'！")
        return

    sonar_image = sonar_image.astype(np.float32)
    sonar_image = cv2.GaussianBlur(sonar_image, (5, 5), 0)
    height, width = sonar_image.shape
    print(f"   图像加载成功，尺寸: {width}x{height}")
    start_time = time.time()
    # --- 第2步：计算归一化强度并估算坡度 ---
    print("2. 正在估算坡度...")
    mean_intensity_per_column = np.mean(sonar_image, axis=0)
    intensity_deviation = sonar_image - np.tile(mean_intensity_per_column, (height, 1))
    low_freq = cv2.GaussianBlur(intensity_deviation, (width//2*2+1, 1), 0)
    # 从原始偏差中减去低频成分，得到高频成分
    high_freq_deviation = intensity_deviation - low_freq
    # c. 使用高频偏差来计算坡度
    slope_x = -k_factor * high_freq_deviation
    print(f"   坡度估算完成，使用了 k_factor = {k_factor} 和高通滤波")

    # --- 第3步：积分坡度得到高度图 ---
    print("3. 正在生成高度图...")
    height_map = np.zeros_like(slope_x, dtype=np.float32)

    # 找到图像的中心列索引
    center_x = width // 2

    # 从中心向右积分
    for x in range(center_x + 1, width):
        height_map[:, x] = height_map[:, x - 1] + slope_x[:, x] * pixel_width_meters

    # 从中心向左积分
    # 注意：这里是从右向左计算，所以是 Z(x) = Z(x+1) - slope(x+1) * dx
    for x in range(center_x - 1, -1, -1):
        height_map[:, x] = height_map[:, x + 1] - slope_x[:, x + 1] * pixel_width_meters
    print("   高度图生成完毕。")

    # --- 第4步：从高度图生成三维点云 ---
    print("4. 正在从高度图生成三维点云...")
    points = []
    # 遍历高度图的每一个像素
    for y in range(height):
        for x in range(width):
            # 将像素坐标 (x,y) 转换为真实世界的三维坐标
            # world_X = x * pixel_width_meters
            # world_Y = y * pixel_height_meters
            # world_Z = height_map[y, x] # Z值来自高度图
            # points.append([world_X, world_Y, world_Z])
            world_X = (width - 1 - x) * pixel_width_meters  # 水平翻转
            # 或者 world_Y = (height - 1 - y) * pixel_height_meters  # 垂直翻转
            world_Y = y * pixel_height_meters
            world_Z = height_map[y, x]  # Z值来自高度图
            points.append([world_X, world_Y, world_Z])
    
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    print(f"   生成了包含 {len(pcd.points)} 个点的三维点云。")
    
    # --- 第5步：从有序点云生成三维网格 ---
    print("5. 正在从点云生成三维网格...")
    # 由于我们的点云是规则的网格结构，我们可以直接从深度图（这里是高度图）创建网格
    # 这是一种非常高效的方式
    # 首先，我们需要将高度图转换为Open3D的Image格式
    # 注意：Open3D的Image需要数据类型为uint16或float32
    # 我们需要对高度图进行归一化或转换以便创建Image对象，但这比较复杂。
    # 一个更直接、更鲁棒的方法是使用BPA算法或直接从点云创建。
    
    # 这里我们使用Ball Pivoting Algorithm (BPA)来创建网格
    print("   正在计算法线...")
    pcd.estimate_normals()
    # pcd.orient_normals_to_align_with_direction()
    pcd.orient_normals_consistent_tangent_plane(100)
    print("   正在运行 Ball Pivoting 算法...")
    # BPA的半径需要根据点云的密度来选择，这里我们基于像素宽度来估计一个值
    radii = [pixel_width_meters, pixel_width_meters * 2]
    mesh = o3d.geometry.TriangleMesh.create_from_point_cloud_ball_pivoting(
        pcd, o3d.utility.DoubleVector(radii))
    print("   三维网格生成完毕。")
    end_time = time.time()
    print(f"   用时: {end_time - start_time:.2f} 秒")
    # --- 第6步：保存和可视化 ---
    if output_mesh_path:
        print(f"6. 正在将3D网格保存到 '{output_mesh_path}'...")
        o3d.io.write_triangle_mesh(output_mesh_path, mesh)
        print("   保存成功！")
    
    if show_plots:
        # 显示2D中间结果
        plt.figure(figsize=(10, 8))
        plt.subplot(2, 1, 1)
        plt.imshow(sonar_image, cmap='gray')
        plt.title('Original Sonar Image')
        plt.subplot(2, 1, 2)
        plt.imshow(height_map, cmap='viridis')
        plt.title('Reconstructed Height Map (Z)')
        plt.colorbar(label='Height (meters)')
        plt.tight_layout()
        plt.show()

    print("\n流程完成！正在显示最终的三维网格模型...")
    print("   (可视化窗口) 按 'q' 关闭窗口。")
    # 为了更好的光照效果
    mesh.compute_vertex_normals()
    o3d.visualization.draw_geometries([mesh])


if __name__ == '__main__':
    # --- 用户配置区域 ---
    # 1. 设置您的单条测线图像的路径
    INPUT_IMAGE_FILE = "crop_R.png"  # <--- !!! 修改这里 !!!

    # 2. 设置输出的.ply网格文件的路径 (可选)
    OUTPUT_MESH_FILE = "crop_R.ply"

    # 3. 设置图像的物理尺度
    #    - X方向 (跨测线方向)
    PIXEL_WIDTH_IN_METERS = 0.12  # <--- !!! 修改这里 !!!
    #    - Y方向 (沿测线方向)
    PIXEL_HEIGHT_IN_METERS = 0.12 # <--- !!! 修改这里 !!!

    # 4. (最重要的参数！) 调整强度到坡度的比例因子 k
    K_FACTOR = -0.005  # <--- !!! 反复调整这个参数 !!!

    # --- 运行SFS重建与3D可视化 ---
    sfs_reconstruct_and_visualize_3d(
        image_path=INPUT_IMAGE_FILE,
        pixel_width_meters=PIXEL_WIDTH_IN_METERS,
        pixel_height_meters=PIXEL_HEIGHT_IN_METERS,
        k_factor=K_FACTOR,
        output_mesh_path=OUTPUT_MESH_FILE,
        show_plots=True
    )
