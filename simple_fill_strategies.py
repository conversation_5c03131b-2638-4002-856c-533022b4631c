"""
简单填充策略集合
提供多种简单有效的填充方法
"""
import torch
import numpy as np
from fvdb import JaggedTensor, GridBatch

def fill_to_seafloor_basic(grid: GridBatch, seafloor_offset=5):
    """
    基础海底填充：从每个体素向下填充到海底
    """
    print(f"执行基础海底填充 (偏移: {seafloor_offset})")
    
    if grid.total_voxels == 0:
        return grid
        
    current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
    if len(current_coords) == 0:
        return grid
    
    # 计算海底高度
    seafloor_z = current_coords[:, 2].min().item() - seafloor_offset
    
    # 现有坐标集合
    existing_coords_set = set(tuple(coord.cpu().numpy()) for coord in current_coords)
    
    # 按列填充
    xy_groups = {}
    for coord in current_coords:
        x, y, z = coord.cpu().numpy()
        key = (int(x), int(y))
        if key not in xy_groups:
            xy_groups[key] = []
        xy_groups[key].append(int(z))
    
    fill_coords_list = []
    for (x, y), z_list in xy_groups.items():
        min_z = min(z_list)
        for z in range(min_z - 1, int(seafloor_z) - 1, -1):
            coord_tuple = (x, y, z)
            if coord_tuple not in existing_coords_set:
                fill_coords_list.append(torch.tensor([x, y, z], device=grid.device))
                existing_coords_set.add(coord_tuple)
    
    if fill_coords_list:
        fill_coords = torch.stack(fill_coords_list)
        grid.enable_ijk(JaggedTensor([fill_coords]))
        print(f"添加 {len(fill_coords)} 个体素")
    
    return grid

def fill_to_seafloor_weighted(grid: GridBatch, seafloor_offset=5, weight_decay=0.8):
    """
    加权海底填充：根据距离表面的深度调整填充概率
    """
    print(f"执行加权海底填充 (偏移: {seafloor_offset}, 衰减: {weight_decay})")
    
    if grid.total_voxels == 0:
        return grid
        
    current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
    if len(current_coords) == 0:
        return grid
    
    seafloor_z = current_coords[:, 2].min().item() - seafloor_offset
    existing_coords_set = set(tuple(coord.cpu().numpy()) for coord in current_coords)
    
    # 按列分组并计算每列的表面高度
    xy_groups = {}
    for coord in current_coords:
        x, y, z = coord.cpu().numpy()
        key = (int(x), int(y))
        if key not in xy_groups:
            xy_groups[key] = []
        xy_groups[key].append(int(z))
    
    fill_coords_list = []
    for (x, y), z_list in xy_groups.items():
        surface_z = max(z_list)  # 表面高度
        min_z = min(z_list)      # 最低点
        
        for z in range(min_z - 1, int(seafloor_z) - 1, -1):
            coord_tuple = (x, y, z)
            if coord_tuple not in existing_coords_set:
                # 根据距离表面的深度计算填充概率
                depth_from_surface = surface_z - z
                fill_probability = weight_decay ** depth_from_surface
                
                # 随机决定是否填充
                if np.random.random() < fill_probability:
                    fill_coords_list.append(torch.tensor([x, y, z], device=grid.device))
                    existing_coords_set.add(coord_tuple)
    
    if fill_coords_list:
        fill_coords = torch.stack(fill_coords_list)
        grid.enable_ijk(JaggedTensor([fill_coords]))
        print(f"添加 {len(fill_coords)} 个体素")
    
    return grid

def fill_to_seafloor_layered(grid: GridBatch, seafloor_offset=5, layer_thickness=2):
    """
    分层海底填充：按层填充，每层有不同的密度
    """
    print(f"执行分层海底填充 (偏移: {seafloor_offset}, 层厚: {layer_thickness})")
    
    if grid.total_voxels == 0:
        return grid
        
    current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
    if len(current_coords) == 0:
        return grid
    
    seafloor_z = current_coords[:, 2].min().item() - seafloor_offset
    existing_coords_set = set(tuple(coord.cpu().numpy()) for coord in current_coords)
    
    # 按列分组
    xy_groups = {}
    for coord in current_coords:
        x, y, z = coord.cpu().numpy()
        key = (int(x), int(y))
        if key not in xy_groups:
            xy_groups[key] = []
        xy_groups[key].append(int(z))
    
    fill_coords_list = []
    for (x, y), z_list in xy_groups.items():
        min_z = min(z_list)
        
        # 分层填充
        current_z = min_z - 1
        layer_count = 0
        
        while current_z > seafloor_z:
            layer_end = max(current_z - layer_thickness, int(seafloor_z))
            
            # 每层的填充密度递减
            layer_density = 1.0 / (layer_count + 1)
            
            for z in range(current_z, layer_end - 1, -1):
                coord_tuple = (x, y, z)
                if coord_tuple not in existing_coords_set:
                    if np.random.random() < layer_density:
                        fill_coords_list.append(torch.tensor([x, y, z], device=grid.device))
                        existing_coords_set.add(coord_tuple)
            
            current_z = layer_end - 1
            layer_count += 1
    
    if fill_coords_list:
        fill_coords = torch.stack(fill_coords_list)
        grid.enable_ijk(JaggedTensor([fill_coords]))
        print(f"添加 {len(fill_coords)} 个体素")
    
    return grid

def fill_to_seafloor_smart(grid: GridBatch, seafloor_offset=5, neighbor_radius=2):
    """
    智能海底填充：考虑邻域密度的填充
    """
    print(f"执行智能海底填充 (偏移: {seafloor_offset}, 邻域半径: {neighbor_radius})")
    
    if grid.total_voxels == 0:
        return grid
        
    current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
    if len(current_coords) == 0:
        return grid
    
    seafloor_z = current_coords[:, 2].min().item() - seafloor_offset
    existing_coords_set = set(tuple(coord.cpu().numpy()) for coord in current_coords)
    
    # 计算每个位置的邻域密度
    def get_neighbor_density(x, y, z, coords_set, radius):
        count = 0
        total = 0
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                for dz in range(-radius, radius + 1):
                    if dx == 0 and dy == 0 and dz == 0:
                        continue
                    neighbor = (x + dx, y + dy, z + dz)
                    total += 1
                    if neighbor in coords_set:
                        count += 1
        return count / total if total > 0 else 0
    
    # 按列分组
    xy_groups = {}
    for coord in current_coords:
        x, y, z = coord.cpu().numpy()
        key = (int(x), int(y))
        if key not in xy_groups:
            xy_groups[key] = []
        xy_groups[key].append(int(z))
    
    fill_coords_list = []
    for (x, y), z_list in xy_groups.items():
        min_z = min(z_list)
        
        for z in range(min_z - 1, int(seafloor_z) - 1, -1):
            coord_tuple = (x, y, z)
            if coord_tuple not in existing_coords_set:
                # 计算邻域密度
                density = get_neighbor_density(x, y, z, existing_coords_set, neighbor_radius)
                
                # 根据邻域密度决定填充概率
                fill_probability = min(0.9, density * 2)  # 密度越高，填充概率越大
                
                if np.random.random() < fill_probability:
                    fill_coords_list.append(torch.tensor([x, y, z], device=grid.device))
                    existing_coords_set.add(coord_tuple)
    
    if fill_coords_list:
        fill_coords = torch.stack(fill_coords_list)
        grid.enable_ijk(JaggedTensor([fill_coords]))
        print(f"添加 {len(fill_coords)} 个体素")
    
    return grid

def fill_to_seafloor_adaptive(grid: GridBatch, seafloor_offset=5):
    """
    自适应海底填充：根据网格密度自动调整填充策略
    """
    print(f"执行自适应海底填充 (偏移: {seafloor_offset})")
    
    if grid.total_voxels == 0:
        return grid
        
    current_coords = grid.ijk[0].jdata[grid.enabled_mask[0].jdata]
    if len(current_coords) == 0:
        return grid
    
    # 分析网格密度
    bbox_size = (grid.bbox[:, 1] - grid.bbox[:, 0]).float()
    volume = bbox_size[0] * bbox_size[1] * bbox_size[2]
    density = grid.total_voxels / volume.item()
    
    print(f"网格密度: {density:.6f}")
    
    # 根据密度选择填充策略
    if density > 0.1:
        # 高密度：使用基础填充
        return fill_to_seafloor_basic(grid, seafloor_offset)
    elif density > 0.01:
        # 中密度：使用加权填充
        return fill_to_seafloor_weighted(grid, seafloor_offset, weight_decay=0.7)
    else:
        # 低密度：使用智能填充
        return fill_to_seafloor_smart(grid, seafloor_offset, neighbor_radius=1)

# 填充策略字典
FILL_STRATEGIES = {
    'basic': fill_to_seafloor_basic,
    'weighted': fill_to_seafloor_weighted,
    'layered': fill_to_seafloor_layered,
    'smart': fill_to_seafloor_smart,
    'adaptive': fill_to_seafloor_adaptive,
}

def apply_fill_strategy(grid: GridBatch, strategy='basic', **kwargs):
    """
    应用指定的填充策略
    Args:
        grid: FVDB网格
        strategy: 填充策略名称
        **kwargs: 策略参数
    Returns:
        填充后的网格
    """
    if strategy not in FILL_STRATEGIES:
        print(f"未知策略: {strategy}, 使用基础策略")
        strategy = 'basic'
    
    print(f"应用填充策略: {strategy}")
    return FILL_STRATEGIES[strategy](grid, **kwargs)

if __name__ == "__main__":
    print("简单填充策略测试")
    print("可用策略:")
    for name in FILL_STRATEGIES.keys():
        print(f"  - {name}")
    
    # 这里可以添加测试代码
