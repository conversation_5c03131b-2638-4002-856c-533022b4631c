#!/usr/bin/env python3
"""
测试空洞填充功能的脚本
"""
import torch
import fvdb
import numpy as np
import src.params as params
from fvdb import JaggedTensor, GridBatch

# 导入填充函数
import sys
sys.path.append('.')
from render import fill_surface_downward, fill_holes_morphological, inv_sigmoid

def create_test_grid():
    """创建一个带有空洞的测试网格"""
    print("创建测试网格...")
    
    # 创建一些测试点云数据
    points = []
    
    # 创建一个带有空洞的表面
    for i in range(-10, 11, 2):
        for j in range(-10, 11, 2):
            # 跳过中心区域创建空洞
            if abs(i) < 4 and abs(j) < 4:
                continue
            # 创建一个简单的表面
            z = 0.1 * (i**2 + j**2) / 100.0
            points.append([i/10.0, j/10.0, z])
    
    points = np.array(points, dtype=np.float32)
    points_tensor = torch.from_numpy(points).cuda()
    
    print(f"创建了 {len(points)} 个测试点")
    
    # 创建稀疏网格
    grid = fvdb.sparse_grid_from_points(
        points_tensor,
        voxel_sizes=0.05,
        origins=[0.0] * 3,
        mutable=True
    )
    
    return grid, points_tensor

def test_surface_fill():
    """测试表面向下填充功能"""
    print("\n=== 测试表面向下填充 ===")
    
    grid, points = create_test_grid()
    
    # 创建初始alpha参数
    alpha = torch.full(
        (grid.total_voxels,), 
        inv_sigmoid(0.5), 
        device=grid.device, 
        requires_grad=True
    )
    
    print(f"填充前网格体素数量: {grid.total_voxels}")
    
    # 执行表面向下填充
    grid_filled, alpha_filled = fill_surface_downward(
        grid, alpha, 
        fill_depth=3, 
        min_alpha_threshold=0.3
    )
    
    print(f"填充后网格体素数量: {grid_filled.total_voxels}")
    print(f"新增体素数量: {grid_filled.total_voxels - grid.total_voxels}")
    
    return grid_filled, alpha_filled

def test_morphological_fill():
    """测试形态学填充功能"""
    print("\n=== 测试形态学填充 ===")
    
    grid, points = create_test_grid()
    
    # 创建初始alpha参数
    alpha = torch.full(
        (grid.total_voxels,), 
        inv_sigmoid(0.5), 
        device=grid.device, 
        requires_grad=True
    )
    
    # 计算边界框
    bbox_min = grid.bbox[:, 0].squeeze() - torch.tensor([2, 2, 2]).cuda()
    bbox_max = grid.bbox[:, 1].squeeze() + torch.tensor([2, 2, 2]).cuda()
    
    print(f"填充前网格体素数量: {grid.total_voxels}")
    
    try:
        # 执行形态学填充
        grid_filled, alpha_filled = fill_holes_morphological(
            grid, alpha, bbox_min, bbox_max,
            kernel_size=3, 
            iterations=2
        )
        
        print(f"填充后网格体素数量: {grid_filled.total_voxels}")
        print(f"新增体素数量: {grid_filled.total_voxels - grid.total_voxels}")
        
        return grid_filled, alpha_filled
        
    except ImportError as e:
        print(f"形态学填充需要额外依赖: {e}")
        print("请安装: pip install opencv-python scipy")
        return grid, alpha

def test_combined_fill():
    """测试组合填充功能"""
    print("\n=== 测试组合填充 ===")
    
    grid, points = create_test_grid()
    
    # 创建初始alpha参数
    alpha = torch.full(
        (grid.total_voxels,), 
        inv_sigmoid(0.5), 
        device=grid.device, 
        requires_grad=True
    )
    
    # 计算边界框
    bbox_min = grid.bbox[:, 0].squeeze() - torch.tensor([2, 2, 2]).cuda()
    bbox_max = grid.bbox[:, 1].squeeze() + torch.tensor([2, 2, 2]).cuda()
    
    print(f"初始网格体素数量: {grid.total_voxels}")
    
    # 先执行表面向下填充
    grid, alpha = fill_surface_downward(
        grid, alpha, 
        fill_depth=2, 
        min_alpha_threshold=0.3
    )
    
    print(f"表面填充后体素数量: {grid.total_voxels}")
    
    # 再执行形态学填充
    try:
        grid, alpha = fill_holes_morphological(
            grid, alpha, bbox_min, bbox_max,
            kernel_size=3, 
            iterations=1
        )
        
        print(f"形态学填充后体素数量: {grid.total_voxels}")
        
    except ImportError:
        print("跳过形态学填充（缺少依赖）")
    
    return grid, alpha

def main():
    """主测试函数"""
    print("开始测试空洞填充功能...")
    
    if not torch.cuda.is_available():
        print("警告: CUDA不可用，测试可能会失败")
        return
    
    try:
        # 测试表面向下填充
        test_surface_fill()
        
        # 测试形态学填充
        test_morphological_fill()
        
        # 测试组合填充
        test_combined_fill()
        
        print("\n=== 所有测试完成 ===")
        print("填充功能已成功集成到render.py中")
        print("您可以通过修改src/params.py中的参数来控制填充行为:")
        print("- use_surface_fill: 启用/禁用表面填充")
        print("- use_morphological_fill: 启用/禁用形态学填充")
        print("- surface_fill_depth: 表面填充深度")
        print("- morphological_kernel_size: 形态学操作核大小")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
