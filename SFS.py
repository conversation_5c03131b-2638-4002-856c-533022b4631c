import cv2
import numpy as np
import matplotlib.pyplot as plt

def reconstruct_with_sfs(image_path, output_heightmap_path, pixel_width_meters, k_factor, show_visualizations=True):
    """
    使用简化的SFS（积分坡度法）从单条声纳图像重建高度图。

    参数:
    image_path (str): 输入的单条测线声纳图像路径。
    output_heightmap_path (str): 输出的高度图保存路径 (e.g., 'sfs_heightmap_pass1.npy')。
    pixel_width_meters (float): 图像中每个像素在真实世界中代表的宽度（米）。
    k_factor (float): 强度到坡度的比例因子，这是一个需要手动调整的关键参数。
    show_visualizations (bool): 是否显示中间过程和最终结果的可视化图。
    """

    # --- 第1步：加载并预处理图像 ---
    print(f"1. 正在从 '{image_path}' 加载图像...")
    # 以灰度模式加载
    sonar_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if sonar_image is None:
        print(f"错误：无法加载图像 '{image_path}'！")
        return

    # 将图像转换为浮点数类型以便计算
    sonar_image = sonar_image.astype(np.float32)

    # 简单的噪声过滤（可选，但推荐）
    sonar_image = cv2.GaussianBlur(sonar_image, (5, 5), 0)

    height, width = sonar_image.shape
    print(f"   图像加载成功，尺寸: {width}x{height}")


    # --- 第2步：计算归一化强度，并估算坡度 ---
    print("2. 正在计算归一化强度并估算坡度...")
    
    # a. 计算每一列的平均强度，作为“平坦海底”的参考
    mean_intensity_per_column = np.mean(sonar_image, axis=0)

    # b. 计算强度偏差（当前强度 - 平均强度）
    # 我们使用一个tile操作将一维的平均强度曲线扩展成与原始图像相同的二维尺寸
    intensity_deviation = sonar_image - np.tile(mean_intensity_per_column, (height, 1))

    # c. 使用简化的线性模型从强度偏差计算坡度
    # slope_x 约等于 k * (I_norm - I_flat)
    # 这里的 k_factor 是一个超参数，需要您根据结果进行调整
    # 它的符号决定了亮是凸起还是凹陷
    slope_x = k_factor * intensity_deviation
    print(f"   坡度估算完成，使用了 k_factor = {k_factor}")


    # --- 第3步：积分坡度得到高度图 ---
    print("3. 正在积分坡度以生成高度图...")
    
    # 创建一个空的浮点数高度图
    height_map = np.zeros_like(slope_x, dtype=np.float32)

    # 沿x方向（跨测线方向）进行逐列累加（积分）
    # Z(x) = Z(x-1) + slope(x) * dx
    for x in range(1, width):
        height_map[:, x] = height_map[:, x - 1] + slope_x[:, x] * pixel_width_meters

    print("   高度图生成完毕。")


    # --- 第4步：保存和可视化 ---
    print(f"4. 正在将高度图数据保存到 '{output_heightmap_path}'...")
    # 保存为.npy文件，这是一种高效的numpy数组存储格式
    np.save(output_heightmap_path, height_map)
    print("   保存成功！")
    
    if show_visualizations:
        print("   正在生成可视化图像...")
        plt.figure(figsize=(18, 10))

        # 原始声纳图
        plt.subplot(2, 2, 1)
        plt.imshow(sonar_image, cmap='gray')
        plt.title('Original Sonar Image')
        plt.colorbar(label='Intensity')

        # 强度偏差图
        plt.subplot(2, 2, 2)
        plt.imshow(intensity_deviation, cmap='coolwarm')
        plt.title('Intensity Deviation')
        plt.colorbar(label='Deviation from Mean')
        
        # 坡度图
        plt.subplot(2, 2, 3)
        plt.imshow(slope_x, cmap='coolwarm')
        plt.title('Estimated Slopes (dz/dx)')
        plt.colorbar(label='Slope')

        # 最终高度图
        plt.subplot(2, 2, 4)
        plt.imshow(height_map, cmap='viridis')
        plt.title('Reconstructed Height Map (Z)')
        plt.colorbar(label='Height (meters)')

        plt.tight_layout()
        plt.show()
    for y in range(height_map.height):
        for x in range(height_map.width):
            # 1. 获取2D坐标 (x, y) 和高度值 Z
            height_value_Z = height_map[y, x]

            # 2. 将像素坐标 (x,y) 转换为真实世界的三维坐标
            world_X = x * pixel_width_meters
            world_Y = y * pixel_height_meters
            world_Z = height_value_Z

            # 3. 将这个三维点添加到列表中
            point_cloud.append( (world_X, world_Y, world_Z) )

    # 此时，point_cloud 就是一个稠密的三维点云
    
    # 4. (可选) 将这个规则的点云连接成一个三维网格
    mesh = create_mesh_from_gridded_point_cloud(point_cloud)

if __name__ == '__main__':
    # --- 用户配置区域 ---
    # 1. 设置您的单条测线图像的路径
    INPUT_IMAGE_FILE = "crop_l.png"  # <--- !!! 修改这里 !!!

    # 2. 设置输出的.npy文件的路径
    OUTPUT_HEIGHTMAP_FILE = "./crop_l.npy"

    # 3. 设置图像的物理尺度
    #    例如，如果您的声纳图像宽度为1000像素，真实覆盖宽度为200米，
    #    那么每个像素的宽度就是 200 / 1000 = 0.2 米
    PIXEL_WIDTH_IN_METERS = 0.12  # <--- !!! 修改这里 !!!

    # 4. (最重要的参数！) 调整强度到坡度的比例因子 k
    #    这是一个需要反复试验的“魔法数字”。
    #    - 它的值的大小，决定了重建出的地形的“起伏程度”。值越大，地形越夸张。
    #    - 它的符号（正或负），决定了亮部是凸起还是凹陷。
    #    - 从一个很小的值开始尝试，例如 0.001 或 -0.001
    K_FACTOR = -0.001  # <--- !!! 反复调整这个参数 !!!

    # --- 运行SFS重建 ---
    reconstruct_with_sfs(
        image_path=INPUT_IMAGE_FILE,
        output_heightmap_path=OUTPUT_HEIGHTMAP_FILE,
        pixel_width_meters=PIXEL_WIDTH_IN_METERS,
        k_factor=K_FACTOR,
        show_visualizations=True
    )
