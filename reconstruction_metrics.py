#!/usr/bin/env python3
"""
三维重建质量评估指标工具
支持RMSE、IoU等多种评估指标，用于SSS SLAM声呐重建系统
"""

import numpy as np
import torch
import open3d as o3d
from PIL import Image
import cv2
from sklearn.neighbors import NearestNeighbors
from typing import Tuple, Dict, Optional, Union
import warnings

class ReconstructionMetrics:
    """三维重建质量评估类"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def compute_height_map_rmse(self, 
                               pred_height_path: str, 
                               gt_height_path: str,
                               mask_path: Optional[str] = None,
                               ignore_zero: bool = True) -> Dict[str, float]:
        """
        计算高度图RMSE
        
        Args:
            pred_height_path: 预测高度图路径
            gt_height_path: Ground truth高度图路径  
            mask_path: 可选的掩码图路径，指定有效评估区域
            ignore_zero: 是否忽略零值像素
            
        Returns:
            包含RMSE、MAE、有效像素数等指标的字典
        """
        # 加载图像
        pred_img = np.array(Image.open(pred_height_path).convert('L'), dtype=np.float32)
        gt_img = np.array(Image.open(gt_height_path).convert('L'), dtype=np.float32)
        
        # 确保尺寸匹配
        if pred_img.shape != gt_img.shape:
            pred_img = cv2.resize(pred_img, (gt_img.shape[1], gt_img.shape[0]))
            
        # 应用掩码
        mask = np.ones_like(pred_img, dtype=bool)
        if mask_path:
            mask_img = np.array(Image.open(mask_path).convert('L'))
            mask = mask_img > 128
            
        if ignore_zero:
            mask = mask & (pred_img > 0) & (gt_img > 0)
            
        if np.sum(mask) == 0:
            return {"rmse": float('inf'), "mae": float('inf'), "valid_pixels": 0}
            
        # 计算误差
        pred_valid = pred_img[mask]
        gt_valid = gt_img[mask]
        
        diff = pred_valid - gt_valid
        rmse = np.sqrt(np.mean(diff**2))
        mae = np.mean(np.abs(diff))
        
        return {
            "rmse": float(rmse),
            "mae": float(mae), 
            "valid_pixels": int(np.sum(mask)),
            "total_pixels": int(pred_img.size),
            "coverage": float(np.sum(mask) / pred_img.size)
        }
    
    def compute_pointcloud_rmse(self,
                               pred_pcd_path: str,
                               gt_pcd_path: str,
                               max_distance: float = 1.0) -> Dict[str, float]:
        """
        计算点云RMSE (使用最近邻匹配)
        
        Args:
            pred_pcd_path: 预测点云路径
            gt_pcd_path: Ground truth点云路径
            max_distance: 最大匹配距离阈值
            
        Returns:
            包含RMSE、精度、召回率等指标的字典
        """
        # 加载点云
        pred_pcd = o3d.io.read_point_cloud(pred_pcd_path)
        gt_pcd = o3d.io.read_point_cloud(gt_pcd_path)
        
        pred_points = np.asarray(pred_pcd.points)
        gt_points = np.asarray(gt_pcd.points)
        
        if len(pred_points) == 0 or len(gt_points) == 0:
            return {"rmse": float('inf'), "precision": 0.0, "recall": 0.0}
        
        # 预测点云到GT的距离 (精度)
        nbrs_gt = NearestNeighbors(n_neighbors=1, algorithm='kd_tree').fit(gt_points)
        pred_to_gt_distances, _ = nbrs_gt.kneighbors(pred_points)
        pred_to_gt_distances = pred_to_gt_distances.flatten()
        
        # GT点云到预测的距离 (召回率)  
        nbrs_pred = NearestNeighbors(n_neighbors=1, algorithm='kd_tree').fit(pred_points)
        gt_to_pred_distances, _ = nbrs_pred.kneighbors(gt_points)
        gt_to_pred_distances = gt_to_pred_distances.flatten()
        
        # 计算指标
        precision_mask = pred_to_gt_distances < max_distance
        recall_mask = gt_to_pred_distances < max_distance
        
        precision = np.sum(precision_mask) / len(pred_points)
        recall = np.sum(recall_mask) / len(gt_points)
        
        # RMSE (双向)
        rmse_pred_to_gt = np.sqrt(np.mean(pred_to_gt_distances**2))
        rmse_gt_to_pred = np.sqrt(np.mean(gt_to_pred_distances**2))
        
        return {
            "rmse_pred_to_gt": float(rmse_pred_to_gt),
            "rmse_gt_to_pred": float(rmse_gt_to_pred), 
            "rmse_symmetric": float((rmse_pred_to_gt + rmse_gt_to_pred) / 2),
            "precision": float(precision),
            "recall": float(recall),
            "f1_score": float(2 * precision * recall / (precision + recall)) if (precision + recall) > 0 else 0.0,
            "pred_points": len(pred_points),
            "gt_points": len(gt_points)
        }
    
    def compute_mesh_rmse(self,
                         pred_mesh_path: str, 
                         gt_mesh_path: str,
                         num_samples: int = 10000) -> Dict[str, float]:
        """
        计算网格表面距离RMSE
        
        Args:
            pred_mesh_path: 预测网格路径
            gt_mesh_path: Ground truth网格路径
            num_samples: 采样点数量
            
        Returns:
            包含Hausdorff距离、平均距离等指标的字典
        """
        # 加载网格
        pred_mesh = o3d.io.read_triangle_mesh(pred_mesh_path)
        gt_mesh = o3d.io.read_triangle_mesh(gt_mesh_path)
        
        # 采样表面点
        pred_pcd = pred_mesh.sample_points_uniformly(number_of_points=num_samples)
        gt_pcd = gt_mesh.sample_points_uniformly(number_of_points=num_samples)
        
        pred_points = np.asarray(pred_pcd.points)
        gt_points = np.asarray(gt_pcd.points)
        
        if len(pred_points) == 0 or len(gt_points) == 0:
            return {"hausdorff": float('inf'), "mean_distance": float('inf')}
            
        # 计算距离
        nbrs_gt = NearestNeighbors(n_neighbors=1).fit(gt_points)
        nbrs_pred = NearestNeighbors(n_neighbors=1).fit(pred_points)
        
        pred_to_gt_dist, _ = nbrs_gt.kneighbors(pred_points)
        gt_to_pred_dist, _ = nbrs_pred.kneighbors(gt_points)
        
        pred_to_gt_dist = pred_to_gt_dist.flatten()
        gt_to_pred_dist = gt_to_pred_dist.flatten()
        
        # Hausdorff距离
        hausdorff = max(np.max(pred_to_gt_dist), np.max(gt_to_pred_dist))
        
        # 平均距离 
        mean_distance = (np.mean(pred_to_gt_dist) + np.mean(gt_to_pred_dist)) / 2
        
        return {
            "hausdorff": float(hausdorff),
            "mean_distance": float(mean_distance),
            "rmse": float(np.sqrt((np.mean(pred_to_gt_dist**2) + np.mean(gt_to_pred_dist**2)) / 2))
        }
    
    def compute_binary_iou(self, 
                          pred_binary_path: str,
                          gt_binary_path: str) -> Dict[str, float]:
        """
        计算二值图IoU
        
        Args:
            pred_binary_path: 预测二值图路径
            gt_binary_path: Ground truth二值图路径
            
        Returns:
            包含IoU、精度、召回率等指标的字典
        """
        # 加载二值图
        pred_img = np.array(Image.open(pred_binary_path).convert('L')) > 128
        gt_img = np.array(Image.open(gt_binary_path).convert('L')) > 128
        
        # 确保尺寸匹配
        if pred_img.shape != gt_img.shape:
            pred_img = cv2.resize(pred_img.astype(np.uint8), 
                                 (gt_img.shape[1], gt_img.shape[0])) > 0
        
        # 计算交集和并集
        intersection = np.logical_and(pred_img, gt_img)
        union = np.logical_or(pred_img, gt_img)
        
        if np.sum(union) == 0:
            return {"iou": 1.0, "precision": 1.0, "recall": 1.0}
            
        iou = np.sum(intersection) / np.sum(union)
        
        # 精度和召回率
        if np.sum(pred_img) == 0:
            precision = 0.0
        else:
            precision = np.sum(intersection) / np.sum(pred_img)
            
        if np.sum(gt_img) == 0:
            recall = 0.0  
        else:
            recall = np.sum(intersection) / np.sum(gt_img)
        
        return {
            "iou": float(iou),
            "precision": float(precision), 
            "recall": float(recall),
            "f1_score": float(2 * precision * recall / (precision + recall)) if (precision + recall) > 0 else 0.0
        }
    
    def compute_voxel_iou(self,
                         pred_alpha: torch.Tensor,
                         gt_alpha: torch.Tensor, 
                         threshold: float = 0.1) -> Dict[str, float]:
        """
        计算体素占用IoU
        
        Args:
            pred_alpha: 预测透明度张量
            gt_alpha: Ground truth透明度张量
            threshold: 占用判断阈值
            
        Returns:
            包含IoU等指标的字典
        """
        # 转换为占用概率
        pred_occ = torch.sigmoid(pred_alpha) > threshold
        gt_occ = torch.sigmoid(gt_alpha) > threshold
        
        # 计算IoU
        intersection = torch.logical_and(pred_occ, gt_occ).sum().float()
        union = torch.logical_or(pred_occ, gt_occ).sum().float()
        
        if union == 0:
            iou = 1.0
        else:
            iou = intersection / union
            
        return {
            "voxel_iou": float(iou.cpu()),
            "pred_occupied": int(pred_occ.sum().cpu()),
            "gt_occupied": int(gt_occ.sum().cpu()),
            "total_voxels": int(pred_occ.numel())
        }
    
    def evaluate_reconstruction(self,
                              pred_height_path: str,
                              pred_binary_path: str, 
                              pred_pcd_path: str,
                              pred_mesh_path: str,
                              gt_height_path: Optional[str] = None,
                              gt_binary_path: Optional[str] = None,
                              gt_pcd_path: Optional[str] = None,
                              gt_mesh_path: Optional[str] = None) -> Dict[str, Dict]:
        """
        综合评估重建质量
        
        Args:
            pred_*_path: 预测结果路径
            gt_*_path: Ground truth路径 (可选)
            
        Returns:
            包含所有评估指标的嵌套字典
        """
        results = {}
        
        # 高度图RMSE
        if gt_height_path:
            try:
                results["height_rmse"] = self.compute_height_map_rmse(
                    pred_height_path, gt_height_path)
            except Exception as e:
                print(f"高度图RMSE计算失败: {e}")
                results["height_rmse"] = {"error": str(e)}
        
        # 二值图IoU  
        if gt_binary_path:
            try:
                results["binary_iou"] = self.compute_binary_iou(
                    pred_binary_path, gt_binary_path)
            except Exception as e:
                print(f"二值图IoU计算失败: {e}")
                results["binary_iou"] = {"error": str(e)}
        
        # 点云RMSE
        if gt_pcd_path:
            try:
                results["pointcloud_rmse"] = self.compute_pointcloud_rmse(
                    pred_pcd_path, gt_pcd_path)
            except Exception as e:
                print(f"点云RMSE计算失败: {e}")
                results["pointcloud_rmse"] = {"error": str(e)}
        
        # 网格RMSE
        if gt_mesh_path:
            try:
                results["mesh_rmse"] = self.compute_mesh_rmse(
                    pred_mesh_path, gt_mesh_path)
            except Exception as e:
                print(f"网格RMSE计算失败: {e}")
                results["mesh_rmse"] = {"error": str(e)}
                
        return results
    
    def print_metrics_summary(self, results: Dict[str, Dict]):
        """打印评估结果摘要"""
        print("\n" + "="*50)
        print("三维重建质量评估结果")
        print("="*50)
        
        for metric_type, metrics in results.items():
            if "error" in metrics:
                print(f"\n{metric_type}: 计算失败 - {metrics['error']}")
                continue
                
            print(f"\n{metric_type.upper()}:")
            for key, value in metrics.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.4f}")
                else:
                    print(f"  {key}: {value}")
        
        print("\n" + "="*50)


def main():
    """示例用法"""
    metrics = ReconstructionMetrics()
    
    # 示例评估
    pred_height = "/path/to/pred_height.png"
    gt_height = "/path/to/gt_height.png"
    pred_binary = "/path/to/pred_binary.png"
    gt_binary = "/path/to/gt_binary.png"
    
    # 计算单项指标
    if False:  # 设置为True来运行示例
        height_results = metrics.compute_height_map_rmse(pred_height, gt_height)
        iou_results = metrics.compute_binary_iou(pred_binary, gt_binary)
        
        print("高度图RMSE:", height_results)
        print("二值图IoU:", iou_results)

if __name__ == "__main__":
    main()