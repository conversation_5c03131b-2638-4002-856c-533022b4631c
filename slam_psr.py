import open3d as o3d
import numpy as np
import os
import time
def reconstruct_with_psr(pcd_path, output_path, normal_radius=0.5, normal_max_nn=50, poisson_depth=9, density_quantile=0.05):
    """
    使用泊松表面重建（PSR）从点云文件生成稠密网格。

    参数:
    pcd_path (str): 输入点云文件的路径 (例如 'slam_output.pcd')。
    output_path (str): 输出网格文件的路径 (例如 'psr_mesh.ply')。
    normal_radius (float): 估计法线时使用的邻域半径。
    normal_max_nn (int): 估计法线时使用的最大邻居数。
    poisson_depth (int): 泊松重建的八叉树深度，控制分辨率。
    density_quantile (float): 用于清理网格的密度分位数阈值。
    """

    # --- 第1步：加载点云 ---
    print(f"1. 正在从 '{pcd_path}' 加载点云...")
    if not os.path.exists(pcd_path):
        print(f"错误：文件 '{pcd_path}' 不存在！")
        return

    try:
        pcd = o3d.io.read_point_cloud(pcd_path)
    except Exception as e:
        print(f"加载点云失败: {e}")
        return

    if not pcd.has_points():
        print("错误：加载的点云为空！")
        return
        
    print(f"   加载成功！点云包含 {len(pcd.points)} 个点。")
# 下采样以减少内存需求

    print(f"   正在下采样点云...")
    time_total_start =time.time()
    pcd = pcd.voxel_down_sample(voxel_size=0.01)  # 根据需要调整体素大小

    print(f"   下采样后点云包含 {len(pcd.points)} 个点")
    print(f"   正在过滤异常值...")
    cl, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=4.0)
    pcd = pcd.select_by_index(ind)
    pcd_end_time = time.time()
    pcd_use_time = pcd_end_time - time_total_start
    print(f"   点云处理完成。耗时: {pcd_use_time:.2f} 秒")
    print(f"   过滤后点云包含 {len(pcd.points)} 个点")

    # --- 第2步：估计法线 ---
    print(f"2. 正在估计法线... (半径={normal_radius}, 最大邻居数={normal_max_nn})")
    normal_time_start = time.time()
    pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=normal_radius, max_nn=normal_max_nn)
    )
    
    # 确保所有法线方向一致（例如，都朝上）
    pcd.orient_normals_to_align_with_direction(orientation_reference=np.array([0.0, 0.0, 1.0]))
    normal_time_end = time.time()
    normal_use_time = normal_time_end - normal_time_start
    print(f"   法线估计完成,耗时：{normal_use_time:.2f} 秒")

    # --- 第3步：运行泊松表面重建 ---
    print(f"3. 正在运行泊松表面重建... (深度={poisson_depth})")
    # 在加载点云后添加此代码

    # !!!!!!!!!! 这里是修改之处 !!!!!!!!!!
    # 新版本的 Open3D 不再需要 with_structure=True 参数
    reconstruct_start_time = time.time()
    mesh, densities = o3d.geometry.TriangleMesh.create_from_point_cloud_poisson(
        pcd, depth=poisson_depth
    )
    # !!!!!!!!!! 修改结束 !!!!!!!!!!
    reconstruct_end_time = time.time()
    print(f"   泊松重建完成，耗时：{reconstruct_end_time - reconstruct_start_time:.2f} 秒")

    # --- 第4步：清理网格 ---
    print(f"4. 正在清理网格... (移除密度低于 {density_quantile*100}% 的顶点)")
    densities_array = np.asarray(densities)
    # cleaned_mesh = mesh
    if len(densities_array) == 0:
        print("警告：泊松重建未能生成密度信息，跳过清理步骤。")
        cleaned_mesh = mesh
    else:
        quantile_threshold = np.quantile(densities_array, density_quantile)
        # 注意：新版本的densities可能是一个DoubleVector，需要转换为numpy array
        vertices_to_remove = np.asarray(densities) < quantile_threshold
        
        cleaned_mesh = mesh
        cleaned_mesh.remove_vertices_by_mask(vertices_to_remove)
    reconstruct_true_end_time = time.time()
    print(f"   网格清理完成，耗时：{reconstruct_true_end_time - reconstruct_start_time:.2f} 秒")

    # --- 第5步：保存和可视化 ---
    print(f"5. 正在将最终网格保存到 '{output_path}'...")
    o3d.io.write_triangle_mesh(output_path, cleaned_mesh)
    print("   保存成功！")
    
    print("\n流程完成！正在显示最终的稠密网格...")
    print("   (可视化窗口) 按 'q' 关闭窗口。")
 # !!!!!!!!!! 新增的高度颜色映射代码 !!!!!!!!!!
    # 1. 获取网格顶点坐标
    vertices = np.asarray(cleaned_mesh.vertices)
    
    # 2. 提取高度信息（Z坐标）
    heights = vertices[:, 2]
    
    # 3. 将高度归一化到[0,1]范围
    if len(heights) > 0:
        min_height = np.min(heights)
        max_height = np.max(heights)
        print(f"min_height:{min_height},max_height:{max_height}")
        if max_height > min_height:
            normalized_heights = (heights - min_height) / (max_height - min_height)
        else:
            normalized_heights = np.zeros_like(heights)
        
        # 4. 创建蓝-绿-红颜色映射
        colors = np.zeros((len(vertices), 3))
        for i, h in enumerate(normalized_heights):
            # if h <= 0.45:
            #     # 纯蓝色区域 (h从0到0.5)
            #     colors[i] = [1,1,1]  # 保持纯蓝色
            #     # elif h <= 0.6:
            #     #     # 蓝色到绿色的插值 (h从0.5到0.7)
            #     #     t = (h - 0.4) * 5  # 将[0.5,0.7]映射到[0,1]
            #     #     colors[i] = [0, t, 1-t]  # 从蓝色到绿色
            # elif h <= 0.525: #0.4 -- 0.5
            #     # 蓝色到绿色的插值 
            #     t = (h - 0.45) * 13.333 # 
            #     colors[i] = [0, t, 1-t]  # 从蓝色到绿色
            # elif h <= 0.6:
            #     # 蓝色到绿色的插值 
            #     t = (h - 0.525) * 13.333  #
            #     colors[i] = [t, 1-t, 0]  # 从绿色到红色
            # # elif h<=0.65:   
            # #     # 绿色到红色的插值 (h从0.7到1)
            # #     t = (h - 0.6) * 20  # 将[0.7,1]映射到[0,1]
            # #     # colors[i] = [t, 1-t, 0]  # 从绿色到红色
            # #     colors[i] = [1, 1, 1]  # 从绿色到红色
            # else:
            #     colors[i] = [1, 1, 1]  # 红色
            colors[i] = [0, 0, 1]
        # 5. 应用颜色到网格
        cleaned_mesh.vertex_colors = o3d.utility.Vector3dVector(colors)
        print(f"   已应用基于高度的颜色映射 (高度范围: {min_height:.3f} - {max_height:.3f})")
    else:
        # 如果没有顶点，使用默认灰色
        cleaned_mesh.paint_uniform_color([0.7, 0.7, 0.7])
        print("   警告：网格没有顶点，使用默认灰色")
    
    # 6. 确保顶点法线被计算，以便正确光照
    cleaned_mesh.compute_vertex_normals()
    # !!!!!!!!!! 高度颜色映射代码结束 !!!!!!!!!!

    o3d.visualization.draw_geometries([cleaned_mesh])


if __name__ == '__main__':
    # --- 用户配置区域 ---
    INPUT_PCD_FILE = "merge_map.pcd" # 您的文件名

    OUTPUT_MESH_FILE = "merge_map.ply"

    NORMAL_ESTIMATION_RADIUS = 0.3
    NORMAL_ESTIMATION_MAX_NN = 10

    POISSON_RECONSTRUCTION_DEPTH = 9
    
    DENSITY_CLEANUP_QUANTILE = 0.01

    # --- 运行重建流程 ---
    reconstruct_with_psr(
        pcd_path=INPUT_PCD_FILE,
        output_path=OUTPUT_MESH_FILE,
        normal_radius=NORMAL_ESTIMATION_RADIUS,
        normal_max_nn=NORMAL_ESTIMATION_MAX_NN,
        poisson_depth=POISSON_RECONSTRUCTION_DEPTH,
        density_quantile=DENSITY_CLEANUP_QUANTILE
    )